#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para transcrição de áudio usando Whisper
Autor: Assistente IA
Data: 2025-01-13
"""

import os
import sys
import whisper
from pathlib import Path

def transcrever_com_whisper(caminho_arquivo, modelo="base"):
    """
    Transcreve áudio usando Whisper
    """
    print(f"🎵 Iniciando transcrição com Whisper...")
    print(f"📁 Arquivo: {caminho_arquivo}")
    print(f"🤖 Modelo: {modelo}")
    
    try:
        # Carregar modelo Whisper
        print("⏳ Carregando modelo Whisper...")
        model = whisper.load_model(modelo)
        
        # Transcrever áudio
        print("🎤 Transcrevendo áudio...")
        resultado = model.transcribe(caminho_arquivo, language="pt")
        
        print("✅ Transcrição concluída com sucesso!")
        return resultado
        
    except Exception as e:
        print(f"❌ Erro na transcrição: {e}")
        return None

def converter_para_audio_valido(arquivo_original):
    """
    Tenta converter o arquivo para um formato de áudio válido
    """
    print(f"\n🔄 Tentando converter arquivo para formato válido...")
    
    try:
        from pydub import AudioSegment
        
        # Tentar diferentes formatos
        formatos_teste = ['mp3', 'm4a', 'ogg', 'flac']
        
        for formato in formatos_teste:
            try:
                print(f"📝 Tentando formato: {formato}")
                
                # Carregar áudio
                audio = AudioSegment.from_file(arquivo_original, format=formato)
                
                # Converter para WAV (formato mais compatível)
                arquivo_wav = f"audio_convertido_{formato}.wav"
                
                print(f"💾 Exportando como WAV...")
                audio.export(arquivo_wav, format="wav", parameters=["-ac", "1", "-ar", "16000"])
                
                print(f"✅ Conversão bem-sucedida: {arquivo_wav}")
                return arquivo_wav
                
            except Exception as e:
                print(f"❌ Formato {formato} falhou: {e}")
                continue
                
    except ImportError:
        print("❌ pydub não disponível")
    except Exception as e:
        print(f"❌ Erro geral: {e}")
    
    return None

def analisar_arquivo(caminho_arquivo):
    """
    Analisa o arquivo para entender sua estrutura
    """
    print(f"\n🔍 ANALISANDO ARQUIVO ===")
    
    if not os.path.exists(caminho_arquivo):
        print(f"❌ Arquivo não encontrado: {caminho_arquivo}")
        return False
    
    # Informações básicas
    tamanho = os.path.getsize(caminho_arquivo)
    print(f"📊 Tamanho: {tamanho / (1024*1024):.2f} MB")
    
    # Ler primeiros bytes
    try:
        with open(caminho_arquivo, 'rb') as f:
            header = f.read(32)
            
        print(f"🔬 Primeiros bytes: {header.hex()}")
        
        # Verificar assinaturas conhecidas
        if header.startswith(b'ID3') or header.startswith(b'\xff\xfb'):
            print("🎵 Formato detectado: MP3")
            return True
        elif header.startswith(b'RIFF'):
            print("🎵 Formato detectado: WAV")
            return True
        elif header.startswith(b'ftyp'):
            print("🎵 Formato detectado: M4A/MP4")
            return True
        elif header.startswith(b'OggS'):
            print("🎵 Formato detectado: OGG")
            return True
        elif header.startswith(b'fLaC'):
            print("🎵 Formato detectado: FLAC")
            return True
        elif header.startswith(b'OpusHead'):
            print("🎵 Formato detectado: OPUS")
            return True
        else:
            print("❓ Formato não reconhecido")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao analisar: {e}")
        return False

def main():
    """
    Função principal
    """
    print("🎯 === TRANSCRITOR WHISPER DE ÁUDIO WHATSAPP ===")
    print("=" * 60)
    
    # Arquivo original - ATUALIZADO para pasta audio 4
    arquivo_original = "audio 4/Áudio do WhatsApp de 2025-09-05 à(s) 14.26.41_376e6126.opus"
    
    # Verificar se arquivo existe
    if not os.path.exists(arquivo_original):
        print(f"❌ Arquivo não encontrado: {arquivo_original}")
        return
    
    # Etapa 1: Analisar arquivo
    if not analisar_arquivo(arquivo_original):
        print("⚠️ Arquivo pode não ser um áudio válido")
    
    # Etapa 2: Tentar transcrição direta com Whisper
    print(f"\n🎤 === TENTATIVA 1: TRANSCRIÇÃO DIRETA ===")
    resultado = transcrever_com_whisper(arquivo_original, "base")
    
    if resultado and resultado.get('text'):
        texto = resultado['text']
        salvar_transcricao(texto, arquivo_original, "whisper_direto", resultado)
        return
    
    # Etapa 3: Converter para formato válido
    print(f"\n🔄 === TENTATIVA 2: CONVERSÃO + TRANSCRIÇÃO ===")
    arquivo_convertido = converter_para_audio_valido(arquivo_original)
    
    if arquivo_convertido:
        resultado = transcrever_com_whisper(arquivo_convertido, "base")
        
        if resultado and resultado.get('text'):
            texto = resultado['text']
            salvar_transcricao(texto, arquivo_original, "whisper_convertido", resultado)
            
            # Limpar arquivo temporário
            os.remove(arquivo_convertido)
            return
    
    # Etapa 4: Tentar com modelo maior
    print(f"\n🚀 === TENTATIVA 3: MODELO MAIOR ===")
    resultado = transcrever_com_whisper(arquivo_original, "small")
    
    if resultado and resultado.get('text'):
        texto = resultado['text']
        salvar_transcricao(texto, arquivo_original, "whisper_small", resultado)
        return
    
    # Falha total
    print(f"\n❌ === FALHA NA TRANSCRIÇÃO ===")
    print("Não foi possível transcrever o áudio com nenhuma abordagem.")
    print("\n💡 SUGESTÕES:")
    print("1. Use Google Drive (transcrição automática)")
    print("2. Use Microsoft OneNote (transcrição de áudio)")
    print("3. Use ferramentas online de conversão")
    print("4. Verifique se o arquivo foi extraído corretamente")

def salvar_transcricao(texto, arquivo_original, metodo, resultado_completo=None):
    """
    Salva a transcrição em arquivo
    """
    # Nome do arquivo baseado no arquivo original
    nome_base = os.path.basename(arquivo_original).split('.')[0]
    arquivo_saida = f"transcricao_{nome_base}_{metodo}.txt"
    
    with open(arquivo_saida, 'w', encoding='utf-8') as f:
        f.write(f"🎵 TRANSCRIÇÃO DO ÁUDIO\n")
        f.write(f"=" * 60 + "\n")
        f.write(f"📅 Data: 2025-01-13\n")
        f.write(f"📁 Arquivo: {arquivo_original}\n")
        f.write(f"🤖 Método: {metodo}\n")
        
        if resultado_completo:
            f.write(f"⏱️ Duração: {resultado_completo.get('duration', 'N/A')}s\n")
            f.write(f"🌍 Idioma: {resultado_completo.get('language', 'N/A')}\n")
        
        f.write(f"=" * 60 + "\n\n")
        f.write(texto)
        
        # Adicionar informações adicionais se disponíveis
        if resultado_completo and 'segments' in resultado_completo:
            f.write(f"\n\n📊 SEGMENTOS DETALHADOS:\n")
            f.write(f"-" * 40 + "\n")
            for i, segment in enumerate(resultado_completo['segments']):
                f.write(f"Segmento {i+1}:\n")
                f.write(f"  Início: {segment.get('start', 'N/A')}s\n")
                f.write(f"  Fim: {segment.get('end', 'N/A')}s\n")
                f.write(f"  Texto: {segment.get('text', 'N/A')}\n")
                f.write(f"  Confiança: {segment.get('avg_logprob', 'N/A')}\n")
                f.write(f"\n")
    
    print(f"\n🎉 === TRANSCRIÇÃO SALVA COM SUCESSO! ===")
    print(f"📁 Arquivo: {arquivo_saida}")
    print(f"\n📝 TEXTO TRANSCRITO:\n")
    print("-" * 60)
    print(texto)
    print("-" * 60)
    
    if resultado_completo and 'segments' in resultado_completo:
        print(f"\n📊 Total de segmentos: {len(resultado_completo['segments'])}")
        print(f"⏱️ Duração total: {resultado_completo.get('duration', 'N/A')}s")

if __name__ == "__main__":
    main()
