🎵 TRANSCRIÇÃO DO ÁUDIO
============================================================
📅 Data: 2025-01-13
📁 Arquivo: audio 3/Som_2025_08_20_19_05_09_928.mp3
🤖 Método: whisper_direto
⏱️ Duração: N/As
🌍 Idioma: pt
============================================================

 Então, noite. E o que ele já tem? A gente tem que fazer, né? Nose. Dizem que hoje acaba as 9,5 vezes de jogo. Gana hoje. Hoje vai acabar. Hoje vai acabar 9,5. É 8,5 de aqui. 2 horas e meia. Vou tentar fazer em 2 horas e meia, então, galera. O jogo consiga ficar careco. <PERSON><PERSON><PERSON>, viu. Que a gente já está, por isso. E já está morrido, já, cara. É muita novidade. Vamos que vamos, porque, cara, tem bastante coisa hoje para a gente ver também, tá? Aproveitando da Boas Vindas e Boa Noite aí para todo mundo. Tanto que a galera que está aqui nos 1, está entrando agora aqui. Tem uma galera também que está no YouTube, tá? Então, galera, sejam muito bem-vindos aí. Hoje nós estamos aí no nosso terceiro dia, né, cara. Da Fussai ou TechWeek com o IA. E eu tenho que admitir para vocês que tem bastante coisa para a gente ver, mas eu acho que a gente avançou bastante aí nesses 2 dias. Eu não sei se todo mundo aí participou, né, está participando aí com o corda com isso, mas tem bastante conteúdo que a gente viu, né. É interessante que muitos desses conteúdos e muita coisa que a gente acaba falando bastante. Não são coisas necessariamente ali no topo da pirâmide, né, no código. É o que está fazendo, vamos dizer assim, a fundação para que essa parada aconteça, né. Então, eu acho que nossa função daqui para frente, como desenvolvedores, de fato, é a gente ter esse domínio muito forte desde, obviamente, da linguagem, que é o programa do Framework que você trabalha, mas fundamentalmente em relação a parte de arquitetura de software, arquitetura de solução. E saber, obviamente, pilotar e inteligência artificial, né. Então, assim, não é trivial, né. E por isso que eu acredito que muita gente hoje em dia ainda passa muita raiva com IA, exatamente pelo fato de não ter encontrado um workflow certo, não ter conseguido entender direito toda essa parte de prompt engineer e tudo mais. Então, tem bastante coisa envolvida para você desenvolver, vamos dizer assim, né. É muito louco, né, falar que a gente vai desenvolver softwares modernos. Eu prefiro dizer que é uma nova geração de softwares, que são softwares diferentes do que a gente tem acostumado a desenvolver nos últimos 30 anos, vamos dizer assim, a coisa mudou bastante, né. O que eu normalmente começo sempre falando é, quase 30 anos atrás, a gente tinha o Game of War citando, botando ali para a gente os design patterns. E esses patterns são usados até hoje. O negócio está ali, claro, né, preto no branco que é o que não é independente se você use, se você gosta ou não. Agora, a turma que está acontecendo com IA, aconteceu agora nos últimos 3 anos do mundo de desenvolvimento, e nesse último 6 meses, então, a coisa ainda ela começou a crescer muito mais forte ainda, né. Então, o nosso objetivo aqui é a gente conseguir percorrer esse ecossistema a inteiro, né. Então, somente, pra quem está chegando hoje, galera. A gente passou 3 dias, eu vou compartilhar aqui a minha, a tela aqui do meu computador pra ficar um pouco mais claro, né. Até onde a gente tava, onde a gente parou, pra onde que a gente vai hoje, o que a gente vê, pra que vocês consigam aí se contextualizar e também relembrar um pouquinho do que a gente falou, tá. Porque é bastante informação mesmo, mas eu acredito que vale a pena, né, pelo menos vocês terem essa visão um pouco mais clara do que é possível e do que está mudando em relação à nossa profissão com IA, que tá... Que é muito além, no final das contas, de saber programar usando o cursor ou qualquer coisa desse tipo. Isso aí é só... É a ponta do iceberg. Isso aí já... Em grandes empresas, já é meio que ponto pacífico. Vamos dizer assim, aí nos dias de hoje, tá. Eu só tô subindo aqui, né, todo o material que a gente acabou vendo nesses últimos dias e na nossa agenda, basicamente, tava a gente consegui ver esses pontos aqui, tá. O ponto de partida de A pra desenvolvedores, falamos sobre a arquitetura de software, solução na área de A, a gente falou sobre conta extendinia, a gente fala sobre pronta indinia, a gente falou bastante de workflow pra desenvolvimento, ontem a gente brincou bastante com isso, né. A gente falou sobre MCP, como ferramenta que a gente pode utilizar no dia a dia, mas também a gente falou de MCP como eu poder desenvolver o meu próprio servidor MCP para que eu possa... Para que inteligências artificiais possam fazer consultas nos meus sistemas e tudo mais. Então a gente acabou dando exemplos, a gente falou sobre tools, a gente falou sobre resources, a gente falou sobre prontes, a gente deu exemplo usando até o próprio cloud, pedindo pra gerar propostas coisas extremamente automatizadas, simplesmente utilizando servidores MCPs, tá. Então o grande ponto aí, pessoal, é que o nosso objetivo hoje, agora, eu acho que é um dos dias mais bacanas, tá. Por que que eu tô dizendo isso? Porque é um dia que, além de a gente ver código, é um dia que a gente vai entender conceitos novos principalmente quando a gente tá falando em agentes inteligentes artificial, que na real, grande parte da galera que eu acabo conversando, acaba não sabendo definir necessariamente o que é um agente. Escreva um aí no chat, galera. Quem aqui, no final das contas, mangia do que é um agente realmente inteligente artificial? Se você tiver que definir, né. Então isso aí é um dos pontos que a gente vai focar bastante. Então o nosso objetivo hoje é a gente falar de REG, que é retruívalmente a generation. A gente vai falar um pouco sobre bancos de dados, principalmente bancos de dados vetoriais. Aí a gente vai entrar e realmente entender o que é um agente diá de verdade. A gente vai entender diferenças de agentes de A pra automações. Tem gente que cria uma automação no N8N e fala que cria um agente. A gente vai falar sobre aplicações multiagenteicas, ou seja, como que eu cria uma aplicação que tem diversos agentes se comunicando com os outros para resolver os problemas. A gente vai falar sobre os frameworks que são populares hoje em dia para trabalhar com desenvolvimento de agentes. A gente vai falar sobre design patterns, a gente vai falar sobre boas práticas, a gente vai falar sobre guard rails, que são formas e a gente tentar minimamente proteger o escopo e o que vai acontecer com o comportamento dos nossos agentes. E a gente vai falar também um pouco de evaluation para como que a gente consegue garantir que os resultados das conversas com os meus agentes, com os sistemas meus que integram com inteligência artificial, eles não estão delirando e vão fazer besteira quando eles chegarem ao cliente final. Então, a grande sacada aqui é que a gente tem bastante conteúdo para ver, mas eu vou mostrar exemplos para vocês de agentes funcionando, eu vou mostrar códigos de agentes, eu vou trazer os conceitos de realmente o que é o que não é um agente. Então, essas coisas vão ficar bem bacanas aí na hora que a gente está trabalhando. Fechou? Galera. E somente para a gente relembrar algumas coisinhas. A gente vai emitir um certificado pela faculdade do Fulsaico de Tecnologia, a FCT, a nossa faculdade, para quem participou dos três dias de evento do início ao fim. Então, no final de cada dia, a gente passou, já no dia 1 e dia 2, uma lista de presença. E quem tiver nessas três listas de presença, vai receber a emissão de um certificado pela nossa faculdade. Então, somente para que vocês saibam, normalmente, no final do evento, a gente passa essa lista. E depois, a gente desabilita o link, quem participou dos três dias, vai levar o certificado. Maravilha? Chou de bola, pessoal. Então, essa é a grande sacada de tudo. E eu quero já ir direto ao assunto, somente para lembrar, pessoal, quando a gente está falando inteligência artificial para desenvolvedores, a gente está falando dessa camada aqui, onde nós desenvolvedores somos usuários de modelos de inteligência artificial. Nosso foco aqui não é para discutir Machine Learning, não é para discutir treinamento de modelos e coisas desse tipo. Nosso ponto aqui está mesmo em desenvolvimento de software, que vai se integrar com a e desenvolvimento de software. Como que a gente usa ia para ser mais produtivo? Então, é basicamente esse é o nosso foco aqui nesses três dias. Então, esse aí, o ponto importante para todo mundo semplificar a ciência. Pessoal, eu vou continuar, onde que eu parei, ontem, para quem não pode assistir, os vídeos estão disponíveis no YouTube ainda. Depois, com o evento acabar, nós vamos tirar esses vídeos e eles vão ficar disponíveis somente para quem são alunos da Fussaiq. Então, somente para lembrar também você na Fussaiq, a gente tem cursos. E também, eu não posso deixar de dizer que nós temos também um MBA, um MBA focado em engenharia de software com a. Então, se você quer aprender realmente a, tem um workflow decente para você desenvolver ser mais produtivo, de verdade, independente do tipo e o tamanho do projeto. Se você quer aprender realmente o que está por trás dos novos momentos no mundo da arquitetura de software de solução, para você criar realmente aplicações de grande porte, para trabalhar realmente em presa grande, tudo mais. E também, se você quer aprender a desenvolver a gente, de integrar aplicações com e a fazer entregas, deplage dessas aplicações, esse MBA em engenharia de software, ele foi feito para você, está somente para você saberem, você pode entrar, clicar aqui, solicitar um contato e bater um papo com a nossa equipe, tá? Porque a gente tem turmas que abrem em momentos diferentes, então a gente tem uma próxima turma que vai abrir no final de setembro, tá? Agora, dia 29, mas a gente já tem turma para dezembro, então dependendo da sua intenção, né? Dependendo da situação, a gente consegue fazer negociações um pouco diferentes em relação a facilidade de pagamento, formas de pagamento e coisas desse tipo, tá? Então, se você tiver com interesse, dessa chance clica, solicito o contato, se vai aprender nome e meio, a gente entra em contato, fala, traz todas as possibilidades para você, independente se você quer começar agora, ou se planejar um pouco mais para o final do ano, eventualmente tendo algum benefício aí para vocês. Fechou? Então, essa aqui é a grande sacada, a gente vai fazer sub-lots, então os valores eles vão subir no progressivamente, então a minha dica aqui é, entra em contato com a gente, vamos bater esse papo, principalmente nessa semana, vou pedir aí para a minha equipe, por favor, colocar o link e aí tanto nos 1 quanto no YouTube aí para vocês. Fechou? Maravilha, galera, lembrando que é um MBA reconhecido pelo MAC, certificado pela faculdade e tudo mais. Maravilha? Pessoal, vamos agora, então, focar aqui, da onde a gente parou, né? Ontem a gente terminou basicamente falando sobre Modo e Context Protocol, ou MCP, que façam que a gente tem um novo protocolo aí para que a gente consiga fazer integrações aí com inteligência artificial, tá? O lance aqui é o seguinte, pessoal, nós vamos entrar em agora num momento diferente, porque até agora a gente falou muito de produtividade, a gente deu exemplos e pontos, inclusive de arquiteturas e coisas desse tipo, mas tem algo bem importante aqui, no meio da história, tá? Que é o quê? Estratégias e até formas que, inevitavelmente, você vai ter que fazer para trabalhar com inteligência artificial, quando você está trabalhando com as suas aplicações. Eu vou dar um exemplo aqui para você, tá? Vamos imaginar que você tem aí a sua empresa, e, de repente, a sua empresa ela quer fazer, por exemplo, uma área de atendimento de suporte. Eu vou dar um exemplo aqui, galera, eu estou dando um exemplo assim de um domínio muito fácil para todo mundo entender, mas poderia ser trabalhar com um projeto, com um processo internos, fazer automações, revisar processos, poderia ser qualquer coisa, mas eu estou dando um exemplo que fique bem fácil para todo mundo entender. Então, vamos imaginar que você quer criar ali uma forma que o cliente final possa conversar ali com uma inteligência artificial e tirar dúvidas de suporte. O que acontece? O ponto de tudo isso é que a inteligência artificial ela não tem os dados da sua empresa. Então, normalmente existem estratégias que você pode utilizar para que você consiga fornecer esses dados para que a ia responda o usuário final. Legal. E algumas dessas estratégias, elas são muito caras, algumas dessas estratégias, eventualmente elas são ineficientes, e algumas dessas estratégias a gente consegue equilibrar um pouco. Uma das formas que o mundo, antes, principalmente de ir à generativa, sempre foi acostumado, é o seguinte, você ter um modelo, e o que eu faço aqui nesse modelo? Eu pego dados que eu tenho da minha empresa, e faço uma camada de treinamento em cima desse modelo. Normalmente a gente chama isso de fine-tune. Então, fine-tune você consegue fazer isso para que o modelo ele consiga entender melhor sobre você. Para fazer isso, você precisa de muito dado. Muito dado mesmo. É custoso, e você precisa realmente trabalhar muito pesado naqueles leis de baixo, de machine learning, e tudo mais. E mesmo assim, você tem muito risco ainda de você não ter as informações totalmente acuradas. Ponto importante de tudo isso é que, no final do dia, muitos casos isso aqui é caro e às vezes é ineficiente. Eu não estou falando que você vai descartar a possibilidade de fazer fine-tune em modelos. Não estou dizendo isso. Mas existem algumas outras alternativas que podem facilitar um pouco a nossa vida. Qual que é essa alternativa? É basicamente o seguinte. Vamos imaginar que eu tenho o meu, deixa eu pegar aqui, que eu acho que fica até mais fácil para eu mostrar, que é algo se você pensar básico, se você pensar dessa forma. Eu tenho um usuário, ele consulta o sistema, o sistema consulta o banco de dados e responde para o usuário. Quem aí tem necessariamente alguma familiaridade com isso que eu acabei de mostrar para vocês? Em pessoal, todo mundo, com a IA hoje em dia, a gente também faz isso. Mas nós fazemos isso de uma forma bem diferente. E qual que é a forma diferente que nós fazemos? Nós temos que usar algumas estratégias. Uma estratégia muito conhecida é o seguinte. Eu vou, o usuário vai bater no sistema e quando eu falo usuário, pode ser usuário final, pode ser um outro sistema, pode ser um micro serviço, pode ser o que for. E pessoal, entendam que existe uma diferença enorme entre chatbot e uma gente de A. A gente vai falar sobre isso com mais tarde, mas não pense que qualquer coisa de conversação é um chatbot, porque não é. Então, o seguinte, o que eu posso fazer nesse caso para que eu consiga bater nesse sistema? Esse sistema aqui, no final das contas, ele tem um modelo de inteligência artificial. Então, a impute do usuário vai cair aqui na inteligência artificial. E como que a inteligência artificial vai conseguir acessar meu banco de dados para ler essas informações? Essa aí começa a ser basicamente uma das formas principais que é um desafio. Então, a gente tem esse desafio número 1. Como que o nosso LLM consegue ler os dados do nosso banco de dados? Segunda coisa que é um outro desafio é como que o LLM tendo acesso ao nosso banco de dados, ele vai conseguir responder ao usuário final de forma correta. Então, nesses nossos casos, existe uma técnica que vai funcionar mais ou menos dessa forma. Eu vou fazer o seguinte. Eu vou, no final do dia, ter algo para o meu agente ou para o meu sistema que eu vou consultar os dados no banco de dados. Aí eu vou pegar esses dados e montar um prąpti com ele. Como assim? Montar um prąpti com ele. Algumas ou menos do tipo? Você é especialista em suporte. Contesto e informações para dúvidas do cliente. E daqui eu coloco os dados vindo do banco de dados. E depois aqui embaixo eu coloco pergunta do cliente. E aqui eu coloco a pergunta que o cliente fez. E depois embaixo eu coloco responda educadamente a pergunta baseado no contexto acima. Então, é exatamente dessa forma que grande parte dos sistemas que trabalham com inteligência artificial trabalha. Eles recuperam dados no banco de dados, pego essas informações e concatenam com o prąpti. E aí, o que acontece? Quando o cliente perguntar como que eu abro um tópico no forum, aqui no banco de dados vai ter informação para ir a como se abre um tópico no forum. Aí vai ler isso aqui e vai responder. Porque ela sabe porque a informação que ela precisa está logo em cima. Fez sentido isso para vocês? Então assim, não há nada de mágicos se você perceber. Eu estou fazendo algo óbvio. É mais ou menos assim. Imagina que você não sabe quanto quer um mais um. E você tem que responder alguém. Aí você recebe uma folha na sua frente. Wesley, você vai ensinar para as pessoas como fazer contas. O resultado de um mais um é dois. A pergunta que alguém fez agora é qual o resultado de um mais um? Aí eu vou chegar e falar pessoal, o resultado de um mais um é dois. Porque eu tenho a cola comigo. Você entendeu? Isso aí é um ponto importante. Só uma coisa que educa na real velho. Está ficando um pouco chato você flutar o chat escrevendo a paligada, tipo taligado e etc. Então por favor pare de flutar o chat. Se você continuar fludando a gente vai ficar com você. Com toda educação do mundo. Acaba atrapalhando ali em raciocínio minha e das pessoas que estão aí no chat. Fechou? Então pessoal, o lanço é o seguinte. É como se você desce pra ir a uma cola de uma prova e embaixo tais perguntas. Então ela vai olhar o que está em cima e vai responder. Fechou? Isso aí é uma forma de você fazer. E é uma forma que é muito utilizada. A ideia principal é essa. O ponto interessante que nós temos que tomar cuidado é que não existe solução simples para a problema complexo. E eu vou começar agora dar alguns exemplos para vocês. Vamos imaginar que eu tenho exatamente essa arquitetura que eu acabei de mostrar para vocês. Mas como que eu consigo no final das contas? É simplesmente pegar essas informações do banco de dados. Por quê? Porque quando eu estou falando de informações, elas não estão todas bonitinhas em um único banco de dados da empresa. Isso aí começa a trazer uma complicação. Porque se você olhar a sua empresa tem informações no site, tem um monte de PDF. Tem um vocobox. Um planilha do Excel. Nesses documentos, eles são usados em diversos estes textos. A gente tem que operar um relatório para mim para os dados do faturamento da empresa. E para isso ela precisa dar planilha do faturamento da empresa. Como que ia ela vai ter acesso a essa planilha? Vocês entendem que nós temos muitas fontes de dados. E como que você consegue jogar essas fontes de dados de uma forma minimamente organizada para ela conseguir fazer essa busca? Isso aí é extremamente complexo. Agora vamos partir do princípio que nós temos a seguinte situação. Vamos imaginar que todo o manual existe na minha empresa de suporte, por exemplo que a gente está colocando aqui embaixo, está aqui no nosso PDF. Eu tenho um suporte.pdf que tem toda a base de suporte que a empresa tem. E ele tem aqui mil páginas. Perfeito? Maravilha. Então, o que que acontece o seguinte? Se eu seguir essa linha de raciocínio que eu falei aqui para vocês, esse contexto de dados vindo do meu banco de dados que estão nesse PDF, vão ficar aqui embaixo. E eu vou botar mil páginas aqui dentro. E para quem é uma pessoa bem inteligente ou que tem um mínimo de raciocínio básico, consegue perceber que tem algo errado botar mil páginas dentro de um prompt da IA. Perfeito? Acredito que vocês consigam entender que isso de forma geral não é legal. E não é legal por muitos motivos. Porque às vezes, o usuário está perguntando algo que está definido em um único parágrafo. E para isso, ele tem que carregar ali mil páginas. O outro ponto importante aqui é que você vai gastar uma furtuna de dinheiro para ir a processar esse monte de informação por conta dos tokens. Se você for ainda mais louco, você vai pegar todos os documentos de botar no prompt e é uma hora, o seu modelo ainda nem vai ter janela de contexto para conseguir suportar todo esse material. Então, basicamente, é algo que vai aumentar a latência, é algo que vai ficar mais custoso, é algo que simplesmente não faz sentido. Para quem é desenvolvedor, na hora que a gente fala algo desse tipo, já vai cheirar mal. Agora, como que nós conseguimos resolver esse tipo de problema, ou minimizar esse tipo de problema? O lance é o seguinte, eu posso fazer o seguinte, eu posso pegar essas minhas mil páginas e transformar essas mil páginas em mil documentos separados. E aí, eu vou pegar esses mil documentos separados e cada documento separado desse vai ser uma linha no banco de dados. Perfeito? Conseguir o capitato aí? Então, na hora que eu fizer uma pergunta como abrir um ticket de suporte, vai cair na LLM, a LLM vai consultar, a gente vai consultar com o seu dinheiro. Ela vai consultar o banco de dados, não é necessariamente a LLM, vai consultar o banco, ela pode usar uma tu para fazer isso, mas isso é uma outra questão. E ela vai fazer uma busca aqui de como criar um ticket de suporte. O problema no final das contas é como que a gente consegue fazer uma busca que vá trazer para a gente desses mil documentos separados, informações realmente relevantes em relação à pergunta do usuário. Porque às vezes uma pergunta como abrir um ticket de suporte pode ser simples, mas eu posso perguntar como que funciona o processo interno de suporte para o treino a meus funcionários, para que eles consigam. A abrir um ticket é ao mesmo tempo, eles conseguirem fazer essa resposta, eles entenderem um pouco melhor o passo a passo de como responder e uma guide line do que poder falar ou não. Você concorda comigo que na hora que a gente faz e joga essa pergunta para ir a como ela vai conseguir pegar essa pergunta que às vezes é mais do que uma pergunta e buscar isso no banco de dados. Então, existe uma forma de fazer isso. Essa forma que normalmente a gente faz é o seguinte, nós pegamos essas mil páginas e realmente nós separamos em mil documentos. E quando eu falo em mil documentos eu escolho a quantidade de tokens ou caractérios que eu quero separar. Isso a gente chama de splitter. A gente vai fazer um splitter dessas informações e a gente vai ter mil documentos. Obviamente esses mil documentos vão ter que parar nesse banco de dados, mas o grande problema é como que eu faço a busca para eu trazer os documentos mais relevantes. Então, nesse caso, o que a gente pode fazer é o seguinte, nós podemos pegar um modelo de embedding que, no final das contas, ele funciona basicamente com o seguinte. Você vai pegar o seu texto, o seu conteúdo, ele vai transformar esse texto num vetor. E a divinha só, esses dados vão parar no banco de dados. Então, eu vou ter aqui o meu conteúdo puro explicando como faz a coisa e eu vou ter o vetor desse conteúdo. Quando alguém fizer uma pergunta para mim, o que eu vou fazer? Eu vou pegar a pergunta do usuário, transformar em vetor e vou fazer uma busca no banco de dados das informações vetorizadas. Fê sentido para vocês, galera, o que eu estou falando? O passo a passo de como isso funciona? A mesma para quem nunca ouviu falar sobre isso, então isso a gente faz algo que a gente chama de busca semântica. Ele consegue, basicamente, pegar o vetor da pergunta, comparar com todos os vetores que estão registrados do banco de dados e trazer para mim alguns registros que tenham mais sentido em relação àquela pergunta. É basicamente isso o que acontece. E uma vez que eu tenho esses trechos dessas informações, eu vou colocar essas informações aqui e eu vou responder o cliente à invés de eu carregar mil páginas aqui para a gente. Legal? Então, é basicamente essa ideia crua de como que funciona essa estratégia. Essa estratégia é muito utilizada e ela é chamada de regge. Então, regge, stands for, stands for, regge significa, deixa eu colar aqui, retrieval aumenta de generation. Então, a invés de eu ficar fazendo fine-tuning no modelo, eu ingeto a informação que eu quero com o modelo, ele responda aqui para mim. De forma grosseira funciona dessa forma como que você vai trabalhar com regge. A maior dificuldade pessoal é que mesmo você fazendo essas buscas vetoriais, não necessariamente o conteúdo que você vai receber vai resolver a dor do cliente. Então, a gente pode ter algumas situações bem simples. Então, vamos imaginar o seguinte. Vamos imaginar que eu tenho um documento que eu falo o seguinte no documento, alguma coisa seguinte. Nem um cliente pode ter desconto de 50%. Beleza? Vamos imaginar agora que esse conteúdo aqui está junto com um monte de outro conteúdo aqui. Um monte de outros informações. Aqui tem conteúdo para caramba, lias em cima de lias aqui no meu PDF. Um monte aqui, meu manual de vendas, de como que os vendedores devem trabalhar na empresa. E uma dessas partes desse manual tem em relação a nenhum cliente pode ter desconto de 50%. O que acontece no meio dessa história? Vamos imaginar que o vendedor, ele está falando com uma inteligência artificial para perguntar até quanto de desconto ele pode dar. E eventualmente ele pergunta se ele pode dar desconto de 50%. Agora, vamos imaginar que na hora que a gente fez esse splitter aqui, ele cortou em dois documentos. Ele cortou em dois documentos da seguinte forma. Ele pegou uma parte desse documento aqui, até aqui. Então ele pegou essa parte do documento no documento 1. E essa parte aqui, que é cliente pode ter de 50% de desconto, ficou no segundo documento. Legal? Aí eu fiz minha busca vetorial e a divinha qual documento que eu trouxe para mim inteligência artificial. Eu trouxe exatamente o pedaço que fala cliente pode ter de 50% de desconto. Então na hora que alguém for fazer uma pergunta, aí vai falar assim, cliente pode ter 50% de desconto. Porque o documento que foi cortado cortou a palavra nenhum. Vocês entendem o tipo de problema que a gente pode estar aí no meio dessa história? Faz sentido para você o nível de risco que você pode ter de trabalhar dessa forma? Então a grande sacada aqui nesse ponto é como que eu tento minimizar isso aí? Isso aí é um ponto importante que eu queria aqui trabalhar com vocês. Porque no final das contas, é isso que vai acontecer no dia a dia na empresa. E é isso que já está acontecendo em muitas empresas. Você passar informação errada para inteligência artificial, pelo fato de que as buscas que você fez no banco de dados trouxeram informações incompletas, ou trouxeram informações que fizeram com que ia entender se é exatamente o oposto daquilo que você queria treinar para ela. Fei sentido? Pessoal, todo mundo está conseguindo entender esse tipo de problema que eu estou trazendo aqui para vocês? É bem importante que vocês entendam. Eu acho que não é algo tão complexo de entender se você cortar um documento, você vai informar essa informação errada para ir. Agora, o lance aqui é o seguinte. Como que nós podemos trabalhar de uma forma que evite ou minimize um pouco esse problema? Uma das formas de você minimizar um pouco esses tipos de problema é o seguinte. É você trabalhar com um conceito que é chamado de Overlapping. O que o Overlapping faz no final das contas? Eu falo que eu vou ter documentos de mil caracteres, por exemplo, e com 100 caracteres de Overlapping. O que isso significa no final das contas? Que eu vou, sim, ter meus documentos de mil caracteres. Mas eu vou voltar 100 caracteres para trás e vou também trazer essa informação. Porque se eu trouxer vários documentos, uma atrás do outro, aí ela consegue perceber que um documento é uma contínuação do outro. Então, isso aí acaba minimizando um pouco esse problema. O problema que eu acabei de mostrar para vocês, ele entese, ele é resolvido, nesse caso simples, com a história do Overlapping. Porque ele iria pegar nenhum também e jogar no mesmo documento. Então, isso aí é um ponto, assim, importante para vocês entenderem. Então, basicamente eu tenho mil caracteres, eu vou pegar 100 caracteres anteriores a esse mil e vou jogar aqui para mim também. Beleza? E isso vai acontecer com 100% dos meus documentos. Então, todos os meus documentos, eles vão ter duplicação em uma quantidade de caracteres que você vai colocar ali para ele. Beleza? Então, isso aí é uma das coisas, assim, que são importantes de a gente conseguir trazer e entender ali para a gente. Overlapping, normalmente, ele vai pegar tudo que está atrás. Agora, uma coisa que vocês vão perceber também é que existem algoritmos para você fazer um split que eles são um pouco mais inteligentes. A invés de ele cortar na carne, vamos dizer assim, ou a quantidade de caracteres, ele tenta identificar o que é um parágrafo, o que é uma frase, o que é uma sentença, onde tem ponto. Então, como isso, ele evita encortar, por exemplo, um parágrafo totalmente no meio. Então, quando eu estou falando de cortar documento e etc., na prática também não é tão dessa forma, porque esses algoritmos que fazem esse split, eles têm diversas opções para conseguir minimizar. Então, ele corta um documento até pular de linha, ele começa o outro quando pulou. Então, não necessariamente todos os documentos, nesse caso, teriam exatamente mil caracteres. Fez sentido isso para vocês. Então, isso ajuda a minimizar. E o Overlapping também ajuda a minimizar. Uma outra coisa que vai ajudar a minimizar é que você também, quando você faz essa busca no banco de dados, você tem duas coisas importantes ali para você. Você vai ter o K, que é a quantidade de documentos que você quer. Quanto mais documento você quer, mais chance você tem de trazer mais de informação. Quanto mais informação mais contexto, mais chance de ar responder certo. Quanto mais documento você colocar, mais tokens você gasta, mais caro a solução fica. Então, você tem que encontrar também um número que você vai testar idealmente. Uma outra informação que você vai ter é o score. Porque é o score? Porque, quando a gente está trabalhando com IAA, a gente não está trabalhando com algo determinístico, que é 1 ou 0. Nós estamos trabalhando com probabilidades. Então, o que isso significa? Muitas vezes, aí a minha busca no banco de dados vai trazer, por exemplo, um documento com um score 0.3. Eu falo, cara, um documento com um score de 0.3, provavelmente ele está bem fora para responder de fato o que IAA está precisando trazer. Então, eu não vou usar um documento com score 0.3. Posso colocar uma regra. Eu só vou responder de 0.7 para cima. E se eu não tiver pelo menos tantos documentos, eu vou falar que eu não sei para IAA falar que ela não sabe. Fechou? Então, assim, pessoal, não é pegar qualquer coisa e retornar ali para IAA. Você consegue gerencer a quantidade de documentos, você consegue gerencer o score do resultado para que você tenha mais segurança do que você vai disponibilizar ali para IAA, ali para você conseguir trabalhar. Legal? O Paulo fez uma pergunta interessante. Como que eu vou recuperar isso no banco de dados se você tem que calcular o vetor da busca? E é pelo sei, pelo vetor, como é que é feito isso? Cara, vocês vão ver, eu vou mostrar isso na prática para vocês. Daqui a pouquinho, eu vou mostrar código e vou mostrar isso na prática acontecendo. Esses tipos de banco de dados vetoriais são prontos para isso. O que isso significa que você dá uma busca, passando um vetor, e ele vai retornar os registros que têm os vetores com as maiores probabilidades para você. Então, é basicamente isso que acontece. Não tem muita novidade em relação aí para a gente. A baseado nisso, pessoal, o que vai acontecer aqui no nosso caso? Eu votei a junção de documentos e vou trabalhar dessa forma. Vou dizer uma coisa, dependendo da situação ou do contexto que você está, isso vai resolver o seu problema. De forma geral, você vai ter um grande incidia certo na hora de trabalhar. Então, isso aí é um ponto importante para vocês. Agora, o ponto é que a vida ela não é simples. Por quê? Porque muitas vezes você não tem apenas um PDF. Você tem um monte de documento. E esses montes de documento, eles estão em domínios diferentes, em categorias diferentes. Eles têm informações diferentes, etc. para vocês. Então, eu vou dar um exemplo aqui para que vocês consigam entender um pouquinho na prática, porque eu sabia que essas dúvidas iriam surgir. Eu tenho uma pasta aqui que eu criei no meu Google Docs, que ele chama MBA IA. Beleza? Essa pasta MBA IA no meu Google Drive, ele tem diversos documentos. Quais documentos ele tem aqui? Ele tem um MBA IA Overview. Se eu entrar nesse documento aqui, eu coloquei para ficar bem simples de vocês entenderem. Logo no cabeçalho, algumas informações interessantes. Por exemplo, curso ID MBA IA. Tipo do documento, é um overview. É uma pós-graduação. Ele tem certificado no Mac e ele tem uma duração de 12 meses. E aqui, para mim, tem nome do curso, promessa do curso, contextualização, pilares, formato das aulas, break out rooms, talkies com especialistas, sessões de mentoria, laboratórios práticos, as disciplinas que esse MBA tem, com quem que vou aprender, como é que eu certificado e algumas perguntas frequentes. Então vamos imaginar que eu estou querendo fazer o MBA que possa tirar dúvidas sobre o meu MBA. Beleza? Agora, a questão aqui é o seguinte. Imagina que você chegue e faz uma pergunta o seguinte. Quais são as disciplinas que tem no MBA? Eu vou fazer uma busca vetorial e eu vou fazer o seguinte. Bom, ele está falando no MBA. Então eu vou buscar todo o documento onde o ID tem a MBA no curso. Porque daí eu já elimino qualquer outro tipo de curso que não seja o MBA. Aí eu já funilo a minha busca vetorial. Outra coisa, ele perguntou quais as disciplinas. Então eu vou buscar e provavelmente em um dos documentos, a por conta da busca que o cara perguntou, vai ter um trecho chamado de disciplinas. E aí, baseado nisso, vai ser respondido para a IA. Vamos imaginar que lá no IA no prompt eu coloco as disciplinas do MBA. Mas daí o que acontece? O que é o seguinte? Aí a pessoa pergunta, está, mas o que eu vou ver em fundamentos de arquitetura de software com IA. Nesse documento tem aqui o que tem no conteúdo programático de arquitetura de software com IA. Não tem essa informação. Então o que acontece? Eu posso ter outro tipo de documento, por exemplo, de disciplinas. E daí eu vou pegar qualquer disciplina aqui ao fundamento de arquitetura. Nesse documento de fundamento de arquitetura eu tenho que o curso ID é o MBA. Eu sei que ele é um tipo de disciplina. Eu sei que o ID da disciplina é fundamentos de arquitetura de software com IA. E o Pilar, que faz parte, é o Pilar de arquitetura de software. E aqui tem todos os detalhes da disciplina. Então, quando eu começo a trabalhar dessa forma, se você perceber, eu começo a ter uma estrutura um pouco mais inteligente. Porque se o cara faz uma pergunta geral, eu posso fazer um pré-processamento da praia antes para ela entender a intenção do usuário. Como assim? Vamos imaginar que eu pergunte o seguinte. Me falho mais sobre o MBA em engenharia de software com IA. Essa é a pergunta. Então o que eu vou fazer, primeiramente, galera? Eu posso aqui usar uma estratégia. Eu posso, antes de sair buscando no banco de dados, fazer um pronto pra me IA fazendo o seguinte. Tente entender a intenção do usuário com essa pergunta. E baseado na intenção, define para mim quais são as chances de isso ser uma pergunta geral, ou seja, que é o meu documento de overview, ou uma pergunta específica de alguma coisa. Então, o que vai acontecer? E eu posso falar ainda assim, responda em um JSON do seguinte tipo. Eu posso pedir para ele responder onde o curso ID, MBA IA, porque ele fez uma pergunta do MBA em engenharia de software com IA, ele vai responder pra mim que o tipo de documento que ele quer é um documento de overview, porque ele fez uma pergunta generalista aqui pra mim. Ok? Uma vez que eu tenho esse JSON aqui na hora de eu fazer a busca no meu banco de dados vetorial, o que eu vou fazer? Eu vou fazer o seguinte. Busque usando a pergunta vetorizada do usuário no banco, onde os metadados, digam que o curso ID, MBA IA, e o tipo é o overview. Aí, o que vai acontecer, galera? Vocês conseguem concordar comigo que os registros que eles vão trazer aqui no meu banco de dados vai vir muito menos tranqueira? A chance de ele trazer dados da informação da nossa pós-graduação com Go é muito menor. Vocês concordam comigo? Por que? Porque eu fiz um pré-processamento da pergunta. Eu tentei entender a intenção do usuário na pergunta. E baseado nessa intenção, eu consigo fazer filtros no meu banco de dados pra eu entender melhor quais tipos de documento que eu vou trabalhar. Percebe? Aí a coisa fica um pouco mais fácil pra eu conseguir trabalhar. Na hora que eu for perguntar, eu tenho esses metadados. Então, quando a gente está falando em estruturar os dados no banco de dados, eu tenho o conteúdo puro que vai ser a informação pra IA, eu tenho o vetor desse conteúdo e eu tenho metadados que vão me ajudar a eu fazer os meus filtros das melhores formas pra eu conseguir filtrar essa parada toda. Então, isso aí é um ponto importante. Beleza? Tem gente pergunta, mas aí a não pode gerar uma interpretação errada da interpretação da parte dela, e por isso que nós temos mecanismos de evaluation e testes onde você passa um set de diversas perguntas nos mais diversos tipos de formato e você avalia se ela está respondendo certo ou não. Caso ela não esteja respondendo certo, você vai melhorar o pront. E você vai melhorar o pront falando, se ele fizer perguntas mais gerais, se ele fizer algo que não sei o quê, use dessa forma. Mas se ele falar disciplina, já usa o tipo disciplina. Se ele falar professor, já usa o documento do tipo de professores, entendeu? Então, o pront, você consegue pedir pra IA, entendendo os seus tipos de documento, ela ser muito mais precisa pra trabalhar dessa forma. Então, a grande sacada aí é essa. Tem gente que perguntou, aí a funila ou embedin, não é que ela funila embedin. O embedin da pergunta vai ser o mesmo. O que aí a vai afunilar? Estamos filtros que ela vai fazendo o banco de dados por conta de metadados que você vai extrair baseado na pergunta que a pessoa fez. Então, isso aí é um ponto importante aí pra o que vocês conseguam trabalhar. Depois disso, pessoal, existem outras técnicas, porque você vai receber vários documentos de diversos tipos. Você pode fazer um processo que é chamado de re-rank que você pode pedir pra IA, pegar todos aqueles resultados e re-ranquear por relevância baseado na pergunta, tem muita coisa avançada pra trabalhar com Ragn, tá? Mas isso só queria trazer essa ideia básica aqui pra vocês. Fê sentido isso aqui pra vocês, galera, o que eu acabei de falar de forma geral, eu não quero que ninguém vir um expert em Ragn aqui. Eu só queria que você entendesse a ideia de como que isso é possível você trabalhar. E, obviamente, dá pra perceber que não é trivial, porque você vai ter que pegar todos os documentos e eu cá num banco de dados. Aí você tem que ser paramento a dado, você tem que ter um monte de técnicas pra você conseguir fazer isso. Não é fácil, principalmente quando você tem muita informação. Mas o conceito básico é esse. Pegar a pergunta, consultar no banco o que tem mais de relevante e injetar isso no pronto. Beleza? Como que isso pode ser feito, pessoal, vou mostrar aqui pra vocês verem alguns exemplos, tá? Deixa eu pegar aqui um exemplo simples, e depois eu vou mostrar um pouquinho, menos simples aqui pra vocês, tá? Somente pra vocês saberem, a gente no nosso MBA, nós criamos algumas aulas que a gente tá começando a disponibilizar de inivelamento, porque a gente sentiu que algumas turmas nossas, tinha gente que não conhecia nada de MCP, tinha gente que era mais avançada, tinha gente que queria num módulo mais teórico como o de fundamentos, a gente já vai colocar um pouco mais a mão na massa, então a gente recebeu esse feedback e pra atender os alunos a gente tá criando vários cursos em paralelos pra que elas possam fazer. Um desses cursos é um curso bem tranquilo de Lang Chen, que é um freme work, que te ajuda a trabalhar de uma forma muito mais simples com chamadas aí com inteligência artificial. É algo que é mais simples, eu digo bem simples do tipo isso aqui, tá? Como que eu faço um Hello World pra mim, a... Eu passo Hello World e aí, a me responde, com três linhas eu fiz uma chamada no modelo de A. Vocês entenderem? Vocês entenderem o que eu tô querendo dizer, como a Lang Chen ela consegue abstrair muito pra vocês, eu não vou dar aula de Lang Chen aqui pra vocês, tô querendo dar esse exemplo aqui pra vocês, tá? Então, somente pra vocês entenderem essa ideia aqui, tá? Só pra vocês saberem, existem um trilhão de freme work de ferramentos, tem Lang Chen, Lang Graph que vem em cima do Lang Chen, aí tem observabilidade com Lang Smith, aí você tem IDK, você tem Crueia, aí caras, você tem um monte de ferramenta de A pra fazer isso, tá? Agora, não é que Lang Chen já era, tá? Lang Chen, tá? Ela é base inclusive pra freme work como o Lang Graph, tá? Então é importante você saber exatamente isso, porque você não precisa, às vezes, de um freme work pra fazer algo muito simples, tá? Então, por isso que é importante você entender esses fundamentos e tudo mais, tá? Agora, o que que acontece? O lance é o seguinte, tem uma parada aqui que eu mostro e eu vou trazer esse exemplo pra que vocês vejam que não é difícil, tá? Mas, apesar de ter bastante código e tudo mais, mas ele não é um exemplo difícil, tá? Basicamente nesse exemplo, o que que eu tô fazendo, a grosso modo, eu estou lendo um documento PDF, chamado GPT-5 aqui pra mim como ponto PDF, beleza? Tudo mundo comigo aqui? Maravilha. Segundo passo que eu vou fazer aqui, eu vou fazer o meu splitter, ou seja, eu vou pegar o conteúdo desse meu PDF e usar um cara chamado Recursive Character Texts Displitter, onde eu vou falar pra ele separar em chunks de mil caracteres aqui, com o Overlap de 550. E quando ele fazer esse splitter, ele vai explitar esse documento aqui pra mim. Maravilha, tranquilo aí pra vocês, ou seja, com a regém do documento na memória e fiz o splitter desse documento, e eu vou receber uma lista desses documentos aqui pra mim. Uma vez que eu fiz isso, eu estou criando aqui pra mim, tá, uma lista de documentos que vai pegar o quê? Vai ter um peixe-contente que é o conteúdo do documento que foi explitado, tá? Ou seja, o conteúdo puro mesmo desse documento. E eu criei aqui também, tá? Uma chave de meta data, onde eu vou pegar todos os metadados do PDF, tá? E vou adicionar aqui num dicionário aqui pra mim. Se esse metadado do PDF, que vem em branco, ele nem vai trazer pra mim. Por que eu estou dizendo isso? Quando eu faço o Texts Displitter aqui pra mim, no PDF, ele vai trazer pra mim os metadados do PDF, ou seja, quem gerou PDF, a data do PDF, o autor do PDF, o programa que gerou o PDF, o autor, sabe essas paradas dos metadados? Então é basicamente isso que acaba acontecendo. Então o que eu estou fazendo aqui é criando uma lista de documentos aqui pra mim, onde eu tenho o conteúdo cru do que foi explitado e os metadados que vieram pra mim nesse meu PDF. Beleza? Tudo mundo entendeu? Até agora, galera. Baixo que eu não quero que vocês entendam detalhes de panto, e como é que sei lá, você faz um fora aqui dessa forma, tá? Mas o importante é que vocês entendam que eu tenho uma lista com todos os documentos, com conteúdos documentos e com a parada aqui. Outra coisa que eu fiz aqui, tá? Eu resolvi criar meus IDS personalizados pra cada documento desse. Eu normalmente gosto de criar os índices personalizados dos IDS, porque no nome do índice eu posso colocar documento Overview 1, página 1, documento Overview, página 2. Eu posso colocar documento, profe disciplinas 1, documento disciplina 1, documento, fica mais fácil pra você buscar inclusive nos índices e ter mais facilidades ali pra gente, tá? Então isso aí é um ponto importante pra vocês conseguirem trabalhar, tá? E aí o que eu estou fazendo aqui no meio da história é o seguinte, eu estou falando que eu vou usar um modelo de embedin da opinião. Esse modelo chama text embed in 3-small, que tem uma quantidade de dimensões no vetor, tem o larga que tem mais quantidade de dimensões e existem outros modelos de embedding que não são da opinião, que dá a opinião que você pode utilizar inclusive, tá? E o que acontece agora? Existem diversos tipos de bancos de dados vetoriais, tá? Existem bancos de dados vetoriais que nasceram já com esse propósito, vou dar um exemplo pra você que é o paincon, tá? O paincon ele faz exatamente isso, ele é um banco, ele roda como serviço, você não tem que ficar se preocupando com escala, ele consegue trabalhar de forma cheia diada, de uma forma muito fantástica, ele tem alta performance e mesmo quando você tem milhões e mais milhões de vetores indexados, tá? Agora, se tem um banco de dados que é super útil nos dias de hoje, é o Possegress, tá? O Possegress ele tem extensões, uma dessas extensões é chamado de PGVector, que é uma forma de você conseguir fazer com que o Possegress vira um banco de dados vetorial pra você fazer esse tipo de busca. O PGEVector tem melhorado muito, tá? Mas existem ainda muitos benchmarks que mostram que alguns bancos de dados têm mais performance que o PGEVector, tá? Quando atinge casas de milhões e milhões de vetores? O que eu tô querendo dizer pra você é que muitos casos, se você já trabalha com Possegress, você ativa essa extensão e usa o PGEVector mesmo, entendeu? Agora, a gente tem que entender que se esses bancos de dados crescem muito, sempre tem trade-off, por exemplo, o Pinecon, ele é essa service e você não se preocupa com um monte de coisa, o Possegress pra você conseguir escalar ele, não é fácil, você vai ter que manjar de gerenciar a banco de dados, se você for trabalhar com um chat, você vai ter que trabalhar com sites. Então tem diversas questões aí que são estratégicas da empresa na adoção do tipo de banco de dados que você quer, tá? Nesse caso aqui, eu tô trabalhando com o PGEVector. Então, o que que acontece? Eu crie um estoura aqui chamado PGEVector, que é uma biblioteca, falei que o modelo de embeddement que eu tô utilizando é esse cara aqui, tô falando que o nome da minha collection, o que que é uma collection nome? É, vamos dizer que é uma coleção de documentos que eu vou criar, tá? Ou seja, como se fosse uma tag que eu posso separar com o nome que eu quiser pra eu chamar da forma que eu quiser, tá? Então é basicamente isso aí pra gente. O outra coisa é a conexão com o meu banco de dados, e eu falar pra ele também, pra ele trabalhar com JSON, Binary JSON na hora de ele fazer armazenamento e trabalhar com os dados, inclusive de metadados e coisas desse tipo. Então, o que eu vou fazer depois disso? Eu vou dar um estour, ed documentos, passo a minha lista de documentos que eu fiz o Explit, passei a minha lista de IDS que eu vou ter pra cada um desses documentos, e bomba, executo, e aquele meu GPT-5.PDF que é um documento grande, ele vai ser jogado no meu banco de dados. Ou é, Zleu, não acredito em você, ok? Eu vou aqui no meu banco de dados, vou aqui em tabelas, e você vai ver que tem uma tabela aqui chamada, aqui uma tabela minha aqui chamada, aqui tem um ID, um name que é o GPT-5 collection, que é o nome da collection que eu coloquei, e eu não coloquei nenhum metadado referente à collection, tá? E aqui eu tenho o meu banco de dados com os meus vetores, então o que isso significa, galera? Eu tenho o meu ID, eu tenho o ID da collection, eu tenho o embed que aqui é aquele vetor gigante que é onde a busca é realizada. Eu tenho aqui dado do documento, de qual foi o documento, e eu tenho aqui os meus metadados, e o meu metadado aqui é pagina zero, o caminho do documento que foi carregado, o criador, o mod date, trap, producer, esse cara, total page foi x59, creation date, ou seja, um monte de metadados que veio do PDF aqui pra mim, tá? Deu pra você sacarem o que aconteceu com esse tipo de formato, galera? Fez sentido isso aqui pra vocês? Beleza, então o que eu fiz foi a ingestão de um PDF fazendo aquele split em chanks usando as informações ali pra mim, tá? Beleza? Beleza? Mas simples do que parece, né? Mas é porque a gente tá com um script, né? Como eu disse pra vocês, coisas complexas não dá pra resolver de forma muito simples não. Mas pra esse exemplo dá bom aqui pra que vocês consegui trabalhar, beleza? Rola GeetHub, coisa desse tipo, galera, esse é material pra usar lunes do MBN, em gêneria, tá? Recomenos que você vira lunes, você vai ter muito mais material do que isso, tá bom? Outra coisa aqui, tá, é o seguinte. E eu tenho aqui um exemplo de busca vetorial, tá? Então aqui, basicamente, é a mesma coisa. O que eu vou fazer aqui é que agora eu tenho uma query que vamos dizer que a pergunta do usuário, tá? Então o que que acontece o seguinte? A pergunta é, tell me more about the GPT5 we think in evaluation and performance results comparing to the GPT4. Então essa é a pergunta que vamos dizer o usuário final tá fazendo. Então o que que eu vou fazer aqui? Eu vou pegar, tenho um modelo de embed, tá? Eu tenho o meu store que é o PGVector que eu coloquei. E agora o que que eu vou fazer? Eu vou pegar o meu store, fazer a minha busca por similaridade com score e vou trazer os três primeiros documentos. E perceba que eu estou colocando a query aqui. Galera, isso aqui tá bestraído num nível muito alto, porque por baixo dos planos ele tá gerando um embed de esse cara aqui, transformando isso em vetor, e daí fazendo uma busca comparando o vetor gerado daqui com os vetores que estão no banco, tá? Essa que é a grande questão. É que aqui com LinkedIn tá muito bestraído com Framework. Vocês entenderam o nível de abistração que a gente tá aqui? Beleza? Então esse aí que é a grande pegada, tá? E aqui eu fiz um loop bem simples pra trazer os resultados. Eu mandei trazer três resultados aqui e mandei trazer o score. Deixa eu tentar rodar essa parada aqui pra ver se funciona. Não sei se como é que tá a minha conexão com o banco de dados. Se não funcionar beleza, eu vou colocar Python 5, não é, vetoriais, o Python aqui, ForSurch Vector. Vou dar um enter aqui. E agora ele fez a busca agora aqui pra mim. Então, se vocês olharem aqui nas buscas, aconteceu o seguinte. Olha só. Ele pegou um resultado um score 0.35 bem ruimzinho e ele trouxe o texto com o pedaço, tá? Daquele documento PDF que a gente leu. Tá? E os metadados encontrados foram. É a página 7 do documento. O source é esse, o creator foi isso, a data de modificação, producera, etc. Uma quantidade de páginas do PDF para essa, o creation date é isso aqui, etc. Aí eu tenho o segundo texto, o segundo resultado com score 0.36, que foi esse texto aqui com esses metadados aqui. E eu tenho um outro cara que é 0.37 que tem esse outro pedaço de texto aqui pra mim. Então, é basicamente isso que acaba acontecendo quando a gente está falando especificamente em reg. Aí, com isso eu iria embedar no meu prompt e na hora que eu embedro no meu prompt o meu a minha, vai conseguir responder. Essa que é a ideia básica de como que funciona essa parada aí. Fechou, galera? Deu pra vocês entenderem. Tem gente perguntona como que estima o custo, etc. Normalmente você tem ferramentas de observabilidade, você vai ter custo para o usuário, ou seja, você vai pegar uma média da quantidade de toque em si quer uma pergunta e a quantidade de toque em si saída e ele vai trazer pra você basicamente o custo. Você pode multiplicar pela quantidade de requisições, tem muitas técnicas e tem ferramentas pra fazer isso, inclusive tem ferramentas pra verificar se os resultados que estão sendo gerados são satisfatórios. Então, isso aí é um ponto importante aí pra você saber. Mas, galera, meu ponto aqui não é profundar e dá aula de régui. Eu queria que vocês entendessem que existe esse conceito e que isso é muito utilizado hoje em dia caso você queira trabalhar com IAA. E lembrando, galera, isso não é só pra chatbot, imagina que eu realmente tenho um processo que vai gerar relatórios e pra eu gerar relatórios eu tenho que pegar resultados, tem que pegar presentações, pega a transcrições de reuniões, preciso compilar tudo isso pra gerar um relatório final pra diretoria, no memória, no executivo. Cara, não tem nada a ver com chatbot, mas ainda assim precisa fazer esses processos, entendeu? Então, é bem essa pegada aí pra vocês, tá? Agora, existem as formas que acabam ficando mais complexas, tá? E tipo, vou dar um exemplo da parada do que eu acho que é o de curso, talvez, eu acho que será que era esse cara aqui. Não acho que não é esse cara, deixa eu tentar lembrar qual esse cara que se eu tenho fácil aqui pra mostrar a MCP, talvez seja esse reggae aqui, talvez seja esse cara. Eu acho que eu posso testar, tá? Python, Scripts, demo, ask questions. Aqui é uma gente que faz perguntas sobre aqueles cursos que estão naquele Google Docs, basicamente é isso, tá? Eu falo Mifal, sobre o MBA em engenharia de software com i-A. Aí o que ele vai fazer? Ele vai, vai pesquisar, etc. Vai gerar o embed, ele vai fazer toda aquela parada e vai trazer o resultado, tá? E aqui ele trouxe pra mim a resposta. Claro, o Mbng era de software, é um curso pensado, ele tendo a razão, os pilares se conectam, funciona assim, funciona assado. Perceba, ele trouxe essa parada. Se eu começar a perguntar de disciplina, ele vai filtrar e vai trazer especificamente de uma disciplina. Se eu faço uma pergunta frequente, ele pega dos documentos de pergunta frequente. Se eu falo qual é a diferença de um curso com outro, eu tenho documentos de comparação de um curso com outro pra ficar mais fácil, entendeu? Então isso aí acaba facilitando aí um pouco a nossa vida. Somente pra vocês saberem que dá pra trabalhar com o Rag de diversas formas, tá? Mas a grande questão aqui, que eu quero que vocês entendam agora, é pra gente ir num outro assunto, que é um assunto que ele ainda é muito confuso com conceitos que normalmente a gente não tá acostumado, tá? Nosso desenvolvedores. Então é o seguinte, a gente vai falar sobre agentes de A, galera. E agentes de A, eu já vou querer trazer aqui de cara pra vocês algumas características desses agentes pra que a gente consiga, vamos dizer, desmistificar final de contas o que é um agente, o que não é um agente. Então, o seguinte, pessoal, a gente já é um software como qualquer outro software. Você faz deploy, ele tem código, é um software. Porém, o core desse software é o LLM. O que que significa que as requestes que esse software recebe, eles vão passar, vamos dizer assim, primariamente, pelo modelo de inteligência artificial, decidir o que ele vai fazer. Sabe quando a gente tem o nosso software comum e entra uma request no API REST e daí você cai ali nas suas regras de negócio? Se o cara faz isso, faz isso, se faz isso, se faz isso, se faz isso, se faz isso, se faz isso, faz isso. A gente faz isso hoje, de uma forma determinística, a gente escolhe como que a gente quer fazer isso. É assim que é o software que a gente desenvolve, o grande ponto é que quando a gente está falando de agente de A, não necessariamente a gente tem esses zifes como nós estamos acostumados a fazer. Quem vai definir esses zifes são o modelo de LLM e os promptes que ele recebe. Então, a tomada de decisão do agente vai falar o que deve ser feito. Então, as decisões do que vai ser tomado em determinada situação vai ser realizada pela inteligência artificial. Por padrão, a inteligência artificial ela não trabalha de forma determinística. É muito claro, cada vez que você faz uma pergunta pra IA, ela responde de uma forma diferente. Mesmo que você coloca exatamente o mesmo prompt. Porque ele trabalha com probabilidade, é sempre a probabilidade, a temperatura, que vai mudando o resultado da geração que a IA está fazendo. Existem formas que você consegue gerar mais determinismo pra forçar o agente seguido determinado das etapas. Mas o grande ponto de tudo isso é que uma vez que ela trabalha com probabilidades e no final das contas a probabilidade é qual é o próximo toking? E pra isso, ele usa todo o contexto anterior, você nunca de forma geral vai ter o mesmo resultado. Você pode ter comportamento similares. E por isso que você não pode falar zero ou um, normalmente você vai em relação a parte de probabilidades. Agora, o ponto interessante do agente de IA é que ele tem um papel claro, ou seja, normalmente você vai desenvolver um agente que serve exatamente pra resolver um tipo de problema. Haramente você vai fazer um agente que resolve a todos os problemas do mundo. Porque provavelmente ele não consegue cubrir e ter conhecimento e entender no antes de um espectro muito grande. Outra coisa interessante aqui pra gente é que como cada agente ele tem o seu papel claro, esse agente ele também conhece o ambiente que está em volta dele. O que isso significa? Eu sou um agente que faz por request. Mas eu sei que perto de mim eu tenho um agente que escreve o conteúdo da por request. Eu tenho um agente que normalmente verifica se aquele conteúdo está bom. Então ele consegue entender o que tem volta dele e ele tem ferramentas que você dá pra ele utilizar. Por exemplo, um servidor MCP que a gente trabalhou antes, trabalhou ontem, mas não precisa apenas ser uma ferramenta com o MCP. Por exemplo, eu posso dar uma ferramenta como um Google Search pra ele. Eu toda vez que eu perguntar qual é o resultado do jogo do Corinthians duas semanas atrás. Ele vai falar, poxa, eu não sei o resultado do jogo. O que eu tenho de ferramenta? Ah, eu tenho o Google. Bacana. Deixa eu pesquisar no Google qual é o resultado do jogo. Ele pesquisa no Google, pega o resultado e traz pra você. Manjou? Ah, eu quero saber o faturamento da empresa. Opa, eu tenho um servidor MCP aqui que me retorna o faturamento da empresa. Eu vou chamar. Sacou? Então isso aí é importante que vocês consigam entender. Um agente, ele tem um kit do Batman pra ajudar ele resolver os problemas que ele é especialista. Uma coisa interessante é que ele normalmente ele pode se adaptar e improvisar as coisas. Por exemplo, vamos imaginar que eu pergunto um jogo do Corinthians e ele não acha no Google. Mas daí ele fala, poxa, se eu não agente no Google, deixa eu entrar no site da CBF. Qual que era o nome do campeonato? A beleza. Olha, eu não sei, mas eu tenho um amigo aqui que é especialista em futebol. Deixa eu perguntar pra esse outro agente e esse agente responde pra ele. Carou, a gente funciona basicamente como nós ser humanos funcionamos. A gente conhece as coisas que a gente faz, quando a gente não conhece, a gente pergunta pra outras pessoas e vê outras fontes. É basicamente isso que acaba acontecendo, tá? Uma coisa interessante que a gente se consegue trabalhar e que os nossos softwares, por padrão, não consigam trabalhar por padrão, é. Trabalhar com dados desustruturados. Os nossos softwares dependem muito da sua, do resultado da sua API. Vamos imaginar que eu mandei um JSON. Quem nunca quebrou uma API? Porque o dado era inteiro e mandaram como string e deu pau pra você. Entende? Então, uma gente de há mesmo que receba uma informação que não tá tão estruturada, ainda assim ele consegue inferir, entender a noance daquela informação pra tentar fazer o melhor com que ele pode. Sacou? E esses agentes também podem fazer simulações de cenário antes de tomar uma decisão. Uma coisa interessante também é que esses agentes têm memória de curto e longo prazo. Uma memória de curto prazo é uma memória que tá acontecendo durante aquela seção. Um memória de longo prazo é uma memória que, quando alguém pergunta algo pra ele, ele consegue resgatar isso do passado, entender essa informação e usar essa informação pra continuar trabalhando. E existem diversas técnicas pra você trabalhar com memória. É uma outra seara, é uma outra técnica, é um outro formato de arquitetura pra você trabalhar com memória de longo prazo. E fazendo o Jabá, nosso MBA de engenharia de software conyaça, vai aprender muito sobre isso também. Bote o link aí galera da nossa equipe pra vocês conhecerem mais, pessoal. Entrem em contato, a gente tem duas turmas abertas com datas diferentes e que têm condições de pagamentos eventualmente até diferentes inflexibilidades. Então entra em contato com a nossa equipe pra gente, tá bom? Então, o que acontece? E quem é luno da fussaico de qualquer curso também tem desconto, só pra vocês saberem. Então essa é a parada que um agente faz. Agora a gente tem que tirar algumas confusões que acabam acontecendo no meio da história, tá? Eu já ouvi falar algumas coisas desse tipo que eu quero trabalhar pra você. Uma gente não é uma LLM, mais regra de negócio. Uma gente não é um reggu. Um reggu pode ser simplesmente um software, um script que eu fiz pra fazer ingestão ou uma busca no banco de dados. Mas não necessariamente isso é uma gente. Uma gente ele conhece, ele tem especificidade, ele sabe o que ele tem que fazer, ele sabe como é que ele tem que responder, ele sabe as ferramentas que ele tem. Então aquilo que eu fiz pra vocês e me mostrar ali não é um agente de A. Aquilo ali é uma ferramenta que faz busco em vetor. Sacou qualquer diferença? MCP não é um agente. É um protocolo que um agente pode consultar. Um agente não é um chatbot, tá? Não significa que eu não posso ter um chatbot que é um agente. Mas nem todo chatbot é um agente de A. E a gente tem que tomar muito cuidado com isso, porque hoje em dia falo, ali eu tenho um chatbot, etc. E esse chatbot não use a em picas nenhuma. Entendeu? Ou seja, um chatbot pode ser um agente de A. Mas nem todo chatbot é um agente de A. É importante que vocês consigam entender isso, porque a chatbot ele tem um modifielse e daí você vai clicando, ele vai te navegando e coisas desse tipo. Muito diferente de ser um agente que consegue tomar uma decisão. Por exemplo, posso entrar no chat e falar, olha, eu não gostei desse pedido do último pedido que eu fiz. Na aí o chatbot vai lá consulta no último pedido que o cara fez. Ó, se eu vi que você comprou as baquetas a 5A pra você tocar bateria, você realmente não gostou dela. Por quê? Ai, eu não gostei por isso e por isso. Nossa, eu sinto muito. Olha, se você quiser, você pode fazer devolução, certo? Você gostaria? Sim, então faz a devolução pra mim. Neste momento, o agente vai lá, entro com pedido de devolução, adiciona observação no sistema que o cliente não gostou da baqueta pela qualidade na hora que ele foi tocar. A notóqua foi o cliente, entrou no CRI, me anotou no CRI, me cês cliente, já comprou a baqueta e não gostou daquela marca. E daí ele falou, olha, o seu pedido já foi ali embossado, você vai receber um e-mail. Você consegue perceber o que é um agente de inteligência artificial, consegue fazer tudo com linguagem natural, ele consegue consultar, ele consegue dar baixa impedido, ele consegue fazer uma venda, ele consegue fazer uma compra, ele consegue ajudar. Então, tudo isso você não consegue resolver com o e-fiels, entendeu? Você não consegue resolver com o e-fiel, você ainda mais conversando em linguagem natural. Então, é totalmente possível, vou fazer isso. E o agente pode ser configurado da seguinte forma ainda, eu posso falar, cara, e assim que você fizer em em bolso, verifique se tem um produto similar, e ainda ofereça pra ele, olha, eu vi que você pediu a em bolsa baqueta, mas existe essa baqueta aqui também, que está com um rating de cinco estrelas, você não quer conhecer? É essa aqui, eu acho que eu gostei, posso fazer a compra pra você, ainda te dão um desconto e eu consigo te entregar pra manhã, pode, então tudo bem, o próprio agente vai lá, faz a ordem, faz o pedido, ajusta o frete, tudo mais, e acabou por ali. Sem entender a capacidade que a gente tem com a gente, e vocês conseguiram entender a diferença de uma gente de A, do que um software bem simples, que usa, E A, é muita diferença. Saco, é muita diferença mesmo de uma gente de A, por isso que eu não vou dizer que eu fico bravo, mas quando alguém fala que a gente de A e A chequebote eu quero morrer, porque você não precisa estar falando com o usuário final, entendeu? Eu posso ter agentes que trabalham no meu processo de logística, a um produto saiu defeituoso, a gente entende o produto, consegue perceber onde está as falhas, daí ele manda pra um outro agente que reporta a área de qualidade que a falha está dessa forma, que manda pra um outro agente pra área de engenharia poder revisar essa falha, e já agera ainda uma tarefa pro engenheiro rever aquilo. Cara, perceba que tudo isso é possível, e não é chequebote, entendeu? E aí o grande ponto, como eu falei, agentes, eles têm especialidades, e é por isso que hoje em dia, a coisa mais comum do mundo, é você trabalhar com aplicações multia-genticas ou multia-gentes. Ou seja, cada agente seu tem uma tarefa, ou ela tem um objetivo, capacidades diferentes. Esses agentes eles podem delegar responsabilidades. Esses agentes podem usar outros agentes como ferramenta. Esses agentes podem ter um agente orquestrador que fica recebendo, faz isso o meu amigo, agora você faz isso, eu pego esse resultado, agora eu mando para o outro, tipo um chefe que vai pedindo para o monte de gente fazer as coisas, cada um vai entregando, ele compila tudo e retorna para a diretoria. Tipo isso, sacou? Uma coisa louca é que quando um agente responde o outro, você pode responder como se fosse um humano falando com outro humano. Entendeu? E os agentes, eles podem compartilhar estado, ou seja, eu posso ver, eu pedi para você, por exemplo, gerar um relatório. Eu, como outro, a gente, posso ver qual que é o status do seu relatório, da sua geração de relatório, porque você pode compartilhar comigo o seu estado naquela tarefa que você está fazendo. E o relatório, você pode gerar um documento num bucket lá do Google, por exemplo, como um artefato onde eu vou ter acesso para eu poder baixar. Sacou? Então, é muito louco esse tipo de coisa. E no meio dessa história, a gente tem um monte de framework, um monte de biblioteca, que você consegue usar para criar agentes de A. Então, sim, você consegue criar agentes de A usando Lang Chen. Dá para você criar tranquilamente agentes de A utilizando Lang Chen. O ponto é que, se você for usar Lang Chen para criar um agente de A, provavelmente esse agente de A, ele vai ter, ele vai ser mais simples. Vai ser mais fácil, mas vai ser mais simples. Trabalhar de formas muito complexas, com muitas responsabilidades e coisas desse tipo, podem começar a ficar insustentável trabalhar com Lang Chen pura. Por isso que a gente tem outros frameworks, como o Lang Graph, que usa Lang Chen, para criar basicamente fluxos de como que os agentes trabalham em conjunto. Você tem o Crew AI, que é uma outra forma que você, como se tem uma equipe de agentes, que cada um pega as tarefas, cada um separa umas tarefas, nos caras vão fazer nas tarefas juntos até terminar. Tem outro, a gente que chama Google ADK, que é o que eu vou falar para vocês hoje. Google ADK é o agent de development kit da Google, ele rodou por um ano em produção na Google antes de ser disponibilizado o Open Source. É um projeto que eu gosto muito, ele é bem completo, porque eu consigo trabalhar com ele de forma determinística e não determinística. E ele tem bastante coisa bacana para trabalhar e inclusive vai ser sobre o ADK que a gente vai falar hoje aqui. Então nesse momento, deixe sobe a beber uma água. O Gilson está falando aqui para mim, me perguntou, uma gente pode substituir um telemárqueting e eu vou falar que sim, tá Gilson? E eu vou falar que sim, com ganho, com experiência prática. Esses dias eu recebi uma ligação do meu banco e a primeira palavra que falou foi, somente para você saber nesse momento a sua ligação está sendo gravada e a interação que você vai estar tendo aqui é com uma gente de inteligência artificial. E depois, em seguida, começou a falar assim, e aí o Eslet, tudo bem? Sim, tudo bem, tal. Opa, eu sou do banco, tal, até onde você tem o financiamento da sua casa e a gente está mudando o internet banking. E eu queria saber se você recebeu e-mail com os novos dados de acesso do internet banking. Eu falei, não, eu não recebi não. Quando você mandou, ele fala, cara, a gente mandou tal dia e o assunto foi esse. Eu falo, ah, legal, eu vou dar um molhado. Bom, se você quiser, eu reenviu agora para você. Pode ser, pode. A tota ir reenviando. Beleza, ah, recebi aqui. Ah, legal, beleza. Então, é só se clicar a funciona dessa forma, se tiver, etc. Tem mais alguma coisa que eu consigo te ajudar, Wesley, alguma dúvida, alguma coisa em relação a isso, o financiamento, etc. Aí eu falei, não, então tudo bem, bota a ed, etc. Cara, o que eu estou dizendo com isso é que se não tivesse sido falado, no início, que era um agente de a falando comigo, eu jamais, em hipótesi alguma, eu saberia que eu estaria falando com a gente, porque era um humano falando. Não tinha lag, era super natural, com intonação, tudo que ele falava, eu recebi o e-mail na hora. Cara, era imperceptível. Eu só consegui descobrir por conta que foi falado o início. Entendeu? Manjou, galera, então, assim, dá para assim um agente substituir um telemárquete com certeza da que para frente vai dar. E é muito louco essa parada aí, tá? Agora o lance é o seguinte, pessoal, vamos falar sobre o Google ADK. O Google ADK é um agente developement kit da Google. A ideia do ADK é que você tenha um agente base, vamos dizer assim, e esse agente base vamos dizer que ele é uma interface. E baseado nesse agente base, eu tenho tipos diferentes de trabalho, que são agent types. O primeiro tipo de agent type é um agente baseado em LLM. O que que significa, galera? O que que significa? Significa que você manda uma requisição para esse agente, ele vai executar um modelo de inteligência artificial e vai responder. Eu tenho outros tipos de agente que são agentes de workflow, ou seja, eu posso criar vários agentes e eu posso pedir que cada agente siga um fluxo, um atrás do outro. Ou eu posso fazer que eles façam coisas em paralela, ou eu posso pedir para insificar em loop até eles resolveram um problema, ou eu posso criar um comportamento completamente diferente e criar um meu agente customizado. Fez sentido para vocês, pessoal, a ideia principal desse Google, a devvelopement kit, então é basicamente essa história. Então, o que acontece? Ele tem alguns componentes aqui que eu queria trazer a explicar aqui para vocês. Então, o primeiro componente, eu acabei falando já agora aqui, que são os tipos de agente. Eu tenho base agent, eu tenho LLM agent e eu tenho os agentes que eu trabalho de orchestração. E, se madristo, os meus agentes têm tuos que são ferramentas, ou seja, essas tuos podem ser ok. Eu posso criar uma função na minha aplicação e fazer essa função virar uma ferramenta. Vamos imaginar que eu crie uma função que é criar um novo pedido. Aí, eu dou essa ferramenta para o agente e toda vez que alguém pediu para ele criar um novo pedido, ele vai ver a tu e ele vai falar, opa, eu tenho uma ferramenta de criar pedidos. Então, eu vou usar essa ferramenta pelo criar o pedido, basicamente isso. Eu tenho uma outra parada que é chamado de Agent Tool, ou seja, é quando eu pego um outro agente que eu conheço, e uso ele como se fosse uma ferramenta minha. Mas, quando eu uso ele como ferramenta, esse agente pode ter as tuos deles que ele vai usar essas tuos para resolver o problema para trazer para o agente principal que está usando ele como ferramenta. Entendeu qualquer ideia, pessoal? E eu tenho, ali, também, tuos de suporte. Ou seja, são ferramentas que vão me ajudar a trabalhar, por exemplo, integrações que eu posso ter com outros sistemas, eu posso ter especificações que a OpenAI consegue usar, e eu posso criar essas ferramentas baseadas nela com Function Tool Calling, eu posso trabalhar com MCPS, então tudo isso são possibilidades que a gente pode inter, para eles saberem o que eles podem fazer. Então, essa é a grande pegada. Outra coisa que os agentes têm, galera, cessões, sessions. O que é uma seção, galera? É o histórico cronológico de eventos. Cada vez que você instiga uma conversa, você está iniciando uma nova seção, e cada conversação, cada mensagem que acaba entrando, vira um histórico daquela seção. E aquele histórico de acordo com o que é falado, vai sendo gravado em um state, que é o estado da conversa, é o momento que está acontecendo aquela operação. O que ele tem também são artefatos nessas seções. Às vezes eu falo pro agente, ele me gera um PDF, e eu gero esse arquivo binário como um artefato, e eu posso usar esse artefato depois para mandar um link ou para passar para outro agente. E as seções também isolam conversas, ou seja, eu consigo criar diversas seções simultâneas, da mesma forma como eu consigo criar vários chats quando eu estou falando com o GPT. E quando a seção é uma nova seção, cada seção tem um ID. E quando se abre um website, você gera uma seção. Basicamente isso. Outra coisa que o ADK também tem como componente, é a parte de memória. Então o que isso significa? Se significa que ele tem a memória curto-prásio, que é o state da seção atual, que é o que ele precisa para continuar conversa e com os resultados dos processamentos dele, de tudo o que ele fez, e ele tem também memória de longo prazo. Que é um memore-service, onde eu posso ter um memore-service em memória, mas cair da sua aplicação, você perde todo o histórico, logo é feito muito mais para test. E você tem outras formas de gravar memória, inclusive serviços do Vertex AI, cara, que é uma opinião dos melhores ecossistemas de serviços para a inteligência artificial, que é da Google, que ele tem próprio ali, inclusive, para você conseguir guardar o histórico de todas as seções. Inclusive, ele tem banco de dados de vetor com foco em Rags, já que já consegue trazer todo esse histórico, que não é o que você foi trabalhar com a gente. É muito louco, tá? Outra coisa também, que eu já falei, mas é uma entidade aqui no ADK, que são os artefatos. Então são arquivos binários nomeados e versionados, o versionamento dele é automático, eu consigo colocar diversos tipos, eu posso falar com PDF, eu posso falar com um doc, eu posso falar com TXT, e ele tem uma integração nativa com GCS, que é o Google Cloud Storage, que é basicamente o S3 do Google. Então você consegue subir e guardar esses artefatos ali também, beleza? Então, como é que funciona somente para a gente ter uma visão geral do ADK aqui no nosso caso, pessoal? Funciona mais ou menos assim, deixa eu copiar aqui, colar para ficar mais fácil, que eu criei uma imagensinha bonitinha aqui para vocês, com todo carinho e amor para vocês, tá? Então ele funciona mais ou menos assim quando você olha o ADK para você entender a estrutura dele. Eu tenho usuário que pode ser um sistema, um outro a gente ou qualquer coisa, ele tem um runner, runner é aquele cara que instancia o processo para começar a execução do a gente. Para você usar um runner, você vai ter que instanciar uma seção para você ter o STATE menigmente, ou seja, para guardar o estado da seção, você tem que ter um membro de service para você guardar e armazenar a memória, a longo ou curto prazo, e você vai ter que ter um artefact service para dizer onde você vai armazenar os dados que foram gerados com o artefatos. Baseado nisso, você vai ter com runner a instanciação dos seus agentes onde você define esses agentes que eles vão trabalhar. Quando você define esses agentes, você fala quais são as ferramentas que esses agentes têm. E muitas vezes esses agentes eles também conseguem através dessas tools chamar sistemas externos. Então é basicamente assim de uma forma bem grosseira, mas que é bem a realidade quando você quiser entender um pouco mais sobre o ADK, é mais ou menos assim que ele funciona. Então se a gente fosse falar da interação dos componentes, isso aqui um pouco mais baixo nível, mas eu vou citar, mas é mais baixo nível. Então eu tenho uma input do usuário, eu vou dar um runner, o runner vai criar um invocação context para ele ter o contexto, aí ele vai executar de forma cíncrona, o agente para receber o contexto, o agente vai chamar a chamada utilizando o context, aí as tools vão modificar os custat, os artefatos, o agente vai gerar os eventos que foram produzidos pelas modificações, e o session service vai persistir essas mudanças que acontecem nesses eventos para a gente. É basicamente isso que eu estou trazendo isso aqui para vocês, tá galera? É basicamente isso aqui. Agora uma coisa que é importante você entender, principalmente na ideia do ADK, é que nós temos conceitos de agentes e subaigentes, tá? Então o que que acontece? A gente verso o subagente, o conceito principal é que existe uma hierarquia definida via parâmetro chamado subagentes, ou seja, eu tenho um agente pai, e um agente pai, e um agente pode ter apenas um pai, basicamente isso, imagina uma árvore de dependências onde o filho tem um agente pai, e ele não pode ter mais de um pai, basicamente é isso, tá? Qual que é a diferença prática de um agente para um subagente? Então isso aqui é importante, eu vou mostrar para vocês exemplos disso aí, para vocês ficarem um pouco mais claros assim, olhando até em código, tá? A diferença prática é a autonomia, porque o agente principal consegue executar as coisas de forma independente ao tono, ele decide quando e como executar as suas tarefas, e ele pode iniciar conversas e processos por conta própria, ou seja, ele é o cara, beleza? Como que eu consigo dar um exemplo aqui para vocês? Deixa eu ver que eu sei que eu tenho um exemplo aqui que eu consigo dar aqui, e de que é simpel, e a gente aqui que eu separei para vocês, tá? seguinte, tá? Vou dar um exemplo muito bobo de um agente aqui para vocês entenderem, cria um agente chamado Doc Finder, ele é um arquivo.py, olha a galera como é que a definição muito assim, ridícula de um agente, obviamente que as coisas ficam muito mais complexas, não é hora que você vai criar softwares e tudo mais, né? Mas basicamente, eu estou falando qual é o modelo que eu vou utilizar, e eu estou instanciando um agente, eu estou falando que o nome do agente é Doc Finder, e a gente, eu estou dando uma descrição, que esse usuário, ele faz busca técnica de documentos e retorna os documentos mais relevantes, eu estou falando o modelo e aqui eu estou dando a instrução, ou seja, eu estou falando o que ele é, eu estou dando, vamos dizer, o meu sistema, a instrução, eu estou colocando assim para ele, você é um assistente especializado em contrato documentação técnica sobre tecnologias, e é importante você apenas pode responder perguntas sobre tecnologias, frameworks, linguagens, etc., logo você precisa garantir que você nunca responderá absolutamente nada fora do escloco, como? Qual a capital do Brasil? Qual o futuramento da empresa tal? Então, o primeiro passo, analisa se a pergunta sobre esse tecnologia, segundo passo, se você concluir que a pergunta não é sobre tecnologia, responda, desculpe, sou especializado apenas em tecnologia, caso a pergunta seja sobre tecnologia, você deve usar uma ferramenta do Google Search para encontrar documentação da internet, você deve retorna a informação encontrada, informar-te estruturado, siti sempre as fontes encontradas, aqui são as ferramentas chamadas Google Search, que é uma ferramenta nativa, que o ADK tem, e eu tenho um alt put e que é answer, então quando eu executo aqui esse agente, o que vai acontecer aqui para mim é que ele vai fazer a consulta do Google, e daí ele vai trazer o resultado ali para mim, tá? Então, é basicamente isso que é uma gente de uma forma bem, bem simples, sendo que essa ferramenta do Google aqui é uma ferramenta que o ADK tem nativa para fazer Google Search, entendeu? Então, essa é a grande pegada aqui para vocês, maravilha? Deu para vocês entenderem a ideia básica da definição de uma gente, isso é definição de uma gente, só que para vocês entenderem qual é a ideia de você criar uma gente, e perceba que o PROMPT é uma das coisas mais importantes aqui para vocês conseguiram trabalhar, tá? Aí, o que que acontece aqui é o seguinte, lembra que eu falei para vocês que a diferença de um agente pai para os outros é a autonomia, então eu vou dar um exemplo aqui para vocês de exemplos de estrutura de agentes de autonomia, então olha só aqui para vocês entenderem, imagina que eu tenho aqui um agente principal chamado de customer services agent, tá? Então ele é um customer service agent e o nome dele é customer service, tá? maravilha, e aqui eu estou definindo dois subagentes, uma gente chama OrderLOOKUP agent, o nome dele é OrderLOOKUP, e ele tem um outro a gente chamado Refund agent, que ele é um processador de fazer rainbows, legal? E aí, o que que eu vou fazer aqui para mim? Eu estou instanciando o meu customer agent, falando que o nome dele é customer service e falando que ele tem dois subagentes, então com essa autonomia, o que isso significa galera? Significa o seguinte, significa que se eu chegar ali para o cliente e um cliente perguntar ali para mim, olha, eu queria saber meu último produto, esse agente de customer service vai falar quais agentes subagentes que eu tenho aqui para trabalhar para mim resolver esse meu problema, ah, eu tenho um agente que faz verificação de ordens de serviço, aí ele manda ali para o Order agent, Order agent pega essa pergunta desse cliente, tá? E verifique a ordem de serviço para mim, aí esse agente aqui vai consultar e vai responder para esse agente os dados do pedido, que esse agente fez, beleza? Maravilha, fez sentido isso aí para vocês galera? Então, o que que acontece aqui para a gente? Que é o seguinte, o agente principal tem autonomia, porque ele pode delegar coisas para outros agentes, ele pode executar coisas de uma forma sem a necessidade de outro cara necessariamente mandar nele ali para ele, tá? Então isso aí é um ponto importante, qual que é o outro aspecto aqui, né? Se o agente principal ele pode executar de forma independente autônoma, o subagente ele é executado apenas quando a gente pai decide, ele não tem controle sobre quando ele vai ser chamado, ele vai funcionar como uma função especializada do pai. Agora qual outra diferença de um agente para um subagente? Tem um outra diferença aí também, essa diferença tá na no escopo, pra onde eles podem transferir o controle, ou seja, o subagente ele interage com o usuário, tá? Isso é chamado de delegation. Então basicamente acontece o seguinte, o agente principal ele pode transferir o controle, pra qualquer agente na hierarquia dele, ele tem a visibilidade completa da estrutura organizacional e ele pode escalar problemas diretamente de cima pra baixo ou de baixo pra cima. Ele consegue ver tudo em volta dele, lembra que eu falei que o agente ele tem especialização e ele entende um ambiente em volta dele? Então é isso que acontece com a gente principal. Outra coisa interessante é que subagentes, o subagente ele é limitado pela hierarquia definida pelo pai, ou seja, ele pode interagir apenas do escopo que o pai permite, ele não pode pular nível hierarquico, pode dar um exemplo aqui pra vocês também, discopo, tá? Vamos imaginar aqui que eu tenho a minha empresa, companhia AI, certo? Essa minha companhia AI é a minha estrutura máxima e ela tem subagentes. Um agente que ela tem é chamado de customer services e agente. O customer services e agente ele tem subagentes. Quais são os subagentes dele? O de order look up para consultar ordem de serviço e para dar um refund, ou seja, para devolver dinheiro de ordem cancelado. Mas a minha empresa também tem um agente de suporte, de técnica ao suporte, que tem um subagente de diagnóstico e eu tenho um subagente de escalar o ticket quando dar algum problema. Legal? Então o que cada um pode fazer aqui nesse caso aqui pra gente, galera? O seguinte, o customer service e agente é o principal, tá? Ele pode transferir as coisas para o pai dele e ele pode transferir as coisas para o técnico ao suporte e ele pode delegar para o order look up. Ou seja, eu consigo delegar coisas para o meu irmão e eu consigo delegar coisas para os meus filhos. Ou seja, eu transfiro coisas para o meu irmão ou eu delegue coisas para os meus filhos. Agora, o order look up, e a gente quer esse agente aqui, ele não pode falar diretamente com o técnico ao suporte, porque ele tá fora da hierarquia do técnico ao suporte, tá? E ele não pode mandar qualquer tipo de informação para o agente principal com a compreens AI, porque ele tá fora da hierarquia também. Entendeu? Então, ou seja, o escopo de cada subagente, ele é definido de acordo com a estrutura organizacional dele. Beleza? E tem outra coisa que é importante aqui que vocês entendam, tá? Que é o seguinte. O agente que suba a gente, o estado de como eles compartilham a informação são diferentes. Por exemplo, o agente principal, ele gerenci o estado dele o contexto dele. Ele decide quais informações que ele quer compartilhar com o subagente, e ele pode manter informações privadas só para ele. O subagente, ele tem que compartilhar o estado dele com o pai dele. Ele não tem estado privado. E ele trabalha com o contexto que foi fornecido pelo pai dele. O que isso significa na prática se a gente for olhar aqui, galera? Tá? Em relação ao estado. Vamos imaginar que eu tenho aqui, ó. O cliente quer cancelar um pedido. Então eu tenho um agente principal de customer service e agente. Eu tenho o customer ID, a história, a minha conversação com o meu cliente, eu tenho o meu nível de autorização, e eu tenho as minhas notas privadas aqui, que é ele é um cliente VIP, eu tenho que ser extra cuidadoso com ele. E eu tenho o meu session context, ou seja, a tarefa atual que ele quer é o cancelamento de uma ordem, e ele está frustrado. O customer muda está frustrado. Eu sei que o cliente está frustrado. Então, quando o cliente pede para cancelar alguma coisa, ele vai compartilhar com o agente de baixo, e vai falar o seguinte, olha, eu tenho o cliente 1, 2, 3, 4, 5. O contexto é que ele quer fazer um cancelamento e ele está frustrado. Perceba que eu não passei para ele, que o cliente está bravo, eu não passei para ele a conversa que eu tive com o cliente, eu não falei meu nível de autorização, eu só passei para o agente aqui de baixo, o que eu precisava passar para ele. Ou seja, eu escolho o que eu vou compartilhar com esse agente. Entendeu? Fez sentido, galera. Então, isso aí é um ponto importante para que vocês consigam se ligar. Então, como é que ficaria um fluxo completo aqui para a gente entender? Eu tenho um agente de customer service, que eu falo, você é o agente principal decista quando usar especialista. Aí eu tenho subagentes, order no cap, você só procure informação de pedidos. Eu tenho o refund processor agent, você só processa a reimbouço, e eu tenho técnica o suporte agent, você só resolve problemas técnicos. Ficou claro aí para todo mundo? Certo? Então, eu tenho um customer service que tem três subagentes ali para ele. Então, vamos imaginar o fluxo de uma conversa. O cliente fala, o meu pedido, um, dois, três, não chegou, e eu quero reimbouço. O agente principal recebe a mensagem, e analisa. Eu preciso da informação do pedido e processar o reimbouço. Aí ele decide, autonomamente, autononumamente, automano. Ele recebe de forma autônoma, chamar o subagente. Aí ele vai chamar quem? Order no cap e a gente. Ele vai falar o seguinte. Busque a informação do pedido, um, dois, três, e retorne os dados para o pai. Ele não vai responder direto para o cliente final. Então, agora, o que acontece? O agente principal recebe os dados do pedido, que o order, o cap e a gente, passou para ele. E ele vai verificar se o reimbouço é possível. E ele decide, chamar o refund processor agent, agora. E ele vai fazer o seguinte. Ele vai pegar o contexto do pedido, e vai falar, processo o reimbouço, ele vai processar, e vai retornar a confirmação do reimbouço para o agente e pai. O agente e pai vai montar a resposta final para o cliente. Seu pedido, um, dois, três foi encontrado, e o reimbouço de 50 reais foi processado. Deu para vocês conseguir entender como é que o agente consegue falar e delegar coisas para o outro? Fez sentido isso para vocês, galera. Agora, vocês concordam comigo que essa parada é um tipo de software completamente diferente do que a gente está acostumado a desenvolver? Arquitetura é outra, o jogo é outro, a tecnologia é outra, o prompt, você tem que trabalhar a forma de como você testar isso é diferente. Não entendeu? Ou seja, as coisas mudam completamente, não é mais o back end que a gente está acostumado, entendeu? E aí você tem que pensar que a gente tem que trabalhar com memória, ele tem que trabalhar com banco de dados, ele tem que trabalhar com os MCPs, com as integrações, ele tem que poder escalar, ele pode rodar com um micro serviço, ele pode fazer deploy-me com Kubernetes. Tudo isso, mas cara, arquitetura de agentes é diferente. Até mesmo para você arquitetar isso aqui, é um processo, cara. Arquitetura de agentes, cara, como que você organiza essa árvore para que fique sustentável e fácil de eu modificar isso conforme eu for adicionando outros agentes do meio da história? Entendeu? Eu vou trazer aqui para vocês, pessoal, uma gente, uma aplicação multa-gente que eu fiz, tá? Até gravei um vídeo no YouTube, outros eu já fiz aqui dessa forma, tá? Deixa eu criar uma nova seção. Eu criei, tá? Um agente principal chamado de bugfinder, tá? O que que acontece aqui nesse agente? A gente, eu tenho um objetivo aqui muito claro para esse cara. Ele vai ficar recebendo logs no servidor, e ele vai verificar se esse log é de erro ou não é um log de erro, tá? Então vamos imaginar que eu vou passar ali para ele o seguinte log aqui para mim. Então ele vai receber um log, eu tenho um agente que recebe um log, ele vai processar, aí ele manda isso para um agente de análise de bugs, e esse gerenciador de análise de bugs, interpretou que esse log é de uma chamada HTTPGET, gerado pelo EngineX, e isso não é um erro logo para por aqui. Beleza? Aí, o que que acontece no meio das coisas, galera? Eu posso criar uma outra seção, por exemplo, e eu posso fazer algo um pouquinho diferente. Eu posso pegar aqui um log de erro, feito pelo um javeiro, e imagina que eu recebi lá no meu New Relic, recebi no meu Lesk Search, sei lá. E colei aqui, ó, é um erro de StackTrace, tá? Só para vocês saberem. E eu vou mandar esse cara para o meu agente. Então o que que vai acontecer? Eu tenho um agente que fica olhando para receber log, aí ele recebeu log, ele vai tomar uma decisão que ele tem que mandar isso para um outro agente que analisa para ver se isso é um bug. E esse agente vai analisar aqui, isso é um bug, tá? Aí, uma vez que ele decidiu que isso é um bug, ele vai criar um draft, um rascunho de uma iixo do GeekHub. Uma vez que ele fez esse rascunho, ele vai passar para um agente que vai revisar essa iixo que ele criou. E ele vai pegar as melhorias que ele quis nessa revisão e mandar para um agente que vai fazer o refinamento dessa iixo para ela ficar bonitista. E depois disso, ela passa para um agente que vai criar uma iixo no GeekHub. E depois disso, vai ser mandado para um agente que vai me notificar no Discord, que aquele meu código teve um bug e uma iixo foi criada no GeekHub ali para mim. É basicamente isso que acaba acontecendo ali nesse caso. Não entendeu? Como é que é a grande pegada, galera? Então tudo isso, ele acaba trazendo aqui para a gente. Então eu tenho request, as responces de cada evento, eu consigo colocar o que a gente respondeu e etc. Eu não sei se eu consigo perguntar, give me the iixo URL. Eu não sei se ele vai continuar conversa ou se ele vai chamar outro agente. Ah, ele trouxe aqui, ó. A iixo que eu gerei foi nesse endereço. Aí eu clico aqui e olha só o que ele fez, galera. Order stable missing em produção database. Description, the order stable missing de produção database e preventive de aplicar o fom process New Order. Foi descobrido, descoberto o estal, hoje atualmente as novas ordens estão falhando, etc. O banco de dados é o posto gris nessa versão, o erro foiesse, do spring, do java, etc. A funcionalidade foi afetada, o impacto do usuário foi esse. Suggestões para você resolver esse problema, consequência de você não resolver esse iixo, tá? Ele é um bug e tem alta prioridade, que foi atribuído aqui para mim. Vocês entenderam, galera, o que está acontecendo no nosso mundo? Imagina que bug simples que começem a acontecer nos nossos sistemas, caiu um para um agente desse, que vai criar uma iixo. E daí você vai ter uma agente autônoma que vai pegar essa iixo, vai resolver o bug e vai criar uma puro request, que vai ter uma gente que vai fazer uma cor de review. E aí você pode decidir para um humano ou não dar um merge ou um humano revisa a cor de review. Vocês conseguem entender, galera, que loucura que dá para fazer baseado em tudo isso. Sacou? E tudo isso, galera, eu criei pelo ADK. Isso é, esse agente aqui, eu fiz utilizando o Google ADK. Ou seja, ele foi, e no meu Discord, aqui não vou abrir meu Discord para vocês, eu recebi uma notificação aqui, falando que um novo iixo foi adicionada ali para mim. Galera, vocês conseguiram perceber que naturalmente você não conseguiria criar um software dessa forma? Quem conseguiria criar um software que fizesse isso dessa forma? Sem IA, não dá para criar. Ponto, porque você não consegue entender um problema, escrever um texto dessa forma, entender o contexto, descobrir tecnologia, colocar funcionalidades afetadas, etc. Cara, simplesmente você não consegue fazer isso. Sacou? Simplesmente você não consegue fazer isso sem IA. Ou seu código vai ser o código que vai ter mais if else da sua vida, entendeu? Então, a grande sacada é basicamente nesse nível que a gente está. Então, por isso que eu estou dizendo que um agente de IA é muito mais que um chatbot. Vocês concordam comigo agora que essa interface aqui que eu estou mostrando no ADK? Acho que eu tenho que dar um voltar. Essa interface que eu estou mostrando aqui no ADK, ela é uma interface apenas de teste e de debug, tá? Mas a entrada e a chamada desse agente poderia ser simplesmente o elésque search, quando caiu um log que você manda a presscar. Fez sentido para vocês, galera? Tá? Agora, o que muita gente deve estar perguntando, é, como é que escala? Como é que é o custo? Galera? Isso é um buraco muito mais embaixo, tá? Isso aqui é um exemplo que jamais você conseguiria escalar, da forma como ele está feito, tá? Só para vocês saberem, tá? Você tem que entender de arquitetura de software, você tem que fazer coisas de custo. Você vai ter que trabalhar com um bet prompt, hein? Você vai ter que fazer com que agente façam processamentos simultâneos para você fazer apenas uma chamada para a LLM, para economizar token. Então, tem muitas estratégias para que você economize, para que você consiga escalar, que você consiga triar. Então, tem muita coisa por trás para você fazer um negócio desse rodem de produção. Mas o que eu estou querendo dizer aqui para vocês é que são infinitas as possibilidades que a gente tem agora de desenvolver software. Sacou? Fez sentido para vocês, galera. Manjou? Tudo o que vocês estão vendo aqui é o ADK. O ADK é bem completo, então ele tem o Framework, tá? Ele tem a interface web, aí, tipo, deixa eu colar, mas um erro aqui, sei lá, cara, deixa eu botar aqui para ele. Quando a hoje eu vou pegar no pé dos javeiros aqui, tá? Vou colocar um outro erro aqui de uma outra sessão, vou botar esse cara aqui para rodar. E eu acho que eu consigo até ver as sessões anteriores, mas vamos deixar esse cara rodar. O que é interessante também aqui, galera, é que quando a gente começa a entrar nesta aspecto, a gente começa a entrar em buracos muito mais embaixo. Como que é a observabilidade? Como que eu sei o que um a gente passou para o outro? Como que eu sei se deu um erro? O erro foi de quem? O meu sistema chamou um outro que chamou um a gente, que chamou um outro sistema. Deu pau. Da onde que eu consigo ver se foi o pau? O pau foi no a gente, foi no sistema, foi no banco de dados. Como é que eu consigo verificar isso? Como é que eu consigo verificar a qualidade que realmente o a gente está garantindo para mim que aquilo é um bug ou não? Vocês entendem que a gente tem outros problemas aí no meio de toda essa história? Então, o ponto importante que eu quero trazer aqui para vocês é que não é só criar o a gente. Se você quiser criar um a gente para a palaria da esquina, ok, se você quiser criar um a gente para o Itaú, para o Núíubenque, para o mercado livre, com certeza não vai ser dessa forma. Mas o conceito que você precisa entender é esse, aqui ele já trossou o link da itchio para mim. E aqui, galera, você consegue ver todos os eventos. Então você eu clico aqui, ele chamou esse cara, como que foi a request, você é um bug finder, execute isso, execute aquilo. Aí, ele pegou todos os passos, todos os parâmetros, mandou para o Itchio Drifter, depois mandou para o review, o review recebeu esses dados, etc. Então você consegue ter o conteúdo que foi gerado, entendeu? E aí, essa parada vai cada vez mais embaixo, porque você consegue pegar cada item da interação e ver como é que foi. Então, esse cara aqui recebeu essa request. E ele trouxe aqui para mim, essa é a resposta. Então, a request foi essa, a resposta foi esse, gastou tanto toque em fez isso, fez aquilo. E o mais louco de tudo é que você consegue chegar aqui, e ver o trace. E aqui eu consigo ver tudo. Teve invocations, executou, a gente chamou LLM, chamou a tu para o logger receiver agent, chamou esse cara, esse cara chamou LLM, pegou ao resultado, mandou para o bug analaiser, eu bug analaiser fez isso, que chamou outro, que chamou outro, que chamou outro, e fez isso, até gerar a última chamada e demorou tanto tempo aqui no meu processo para rodar esse processo. Entendeu? Então, cara, as coisas elas estão melhorando bastante. Os frameworks estão evoluindo rápido, as regras estão evoluindo rápido, e agora a forma de desenvolver software mudou. Galera, quando eu falei para vocês que a nova geração de software, que a gente vai criar, quando eu chego aqui para vocês, para fazer o meu jabado MBA, em arquitetura em gerido de software com IA, e eu falo, assuma o novo perfil de desenvolvedor na era de IA. Você vai ser pelo menos cinco vezes mais produtivo, trabalhando com os workflows que você vai aprender com a gente. Mas o ponto que eu estou querendo colocar aqui é faça a parte da criação, da nova geração, de aplicações orientadas as IA nas maiores empresas do mercado. Vocês conseguiram entender agora o que eu quero dizer com fazer parte da criação, da nova geração de aplicações? Fez sentido para vocês o que é essa nova geração? Porque é um paradigma novo, a arquitetura mudou. A forma de fazer devolpes SRE mudou. Processo desenvolver mudou. E o que?

📊 SEGMENTOS DETALHADOS:
----------------------------------------
Segmento 1:
  Início: 0.0s
  Fim: 2.0s
  Texto:  Então, noite.
  Confiança: -0.6331917072164601

Segmento 2:
  Início: 2.0s
  Fim: 4.0s
  Texto:  E o que ele já tem?
  Confiança: -0.6331917072164601

Segmento 3:
  Início: 4.0s
  Fim: 6.0s
  Texto:  A gente tem que fazer, né?
  Confiança: -0.6331917072164601

Segmento 4:
  Início: 6.0s
  Fim: 8.0s
  Texto:  Nose.
  Confiança: -0.6331917072164601

Segmento 5:
  Início: 8.0s
  Fim: 10.0s
  Texto:  Dizem que hoje acaba as 9,5 vezes de jogo.
  Confiança: -0.6331917072164601

Segmento 6:
  Início: 10.0s
  Fim: 12.0s
  Texto:  Gana hoje.
  Confiança: -0.6331917072164601

Segmento 7:
  Início: 12.0s
  Fim: 14.0s
  Texto:  Hoje vai acabar. Hoje vai acabar 9,5.
  Confiança: -0.6331917072164601

Segmento 8:
  Início: 14.0s
  Fim: 16.0s
  Texto:  É 8,5 de aqui.
  Confiança: -0.6331917072164601

Segmento 9:
  Início: 16.0s
  Fim: 20.0s
  Texto:  2 horas e meia. Vou tentar fazer em 2 horas e meia, então, galera.
  Confiança: -0.6331917072164601

Segmento 10:
  Início: 20.0s
  Fim: 22.0s
  Texto:  O jogo consiga ficar careco.
  Confiança: -0.6331917072164601

Segmento 11:
  Início: 22.0s
  Fim: 24.0s
  Texto:  Obrigado, viu.
  Confiança: -0.6331917072164601

Segmento 12:
  Início: 24.0s
  Fim: 26.0s
  Texto:  Que a gente já está, por isso.
  Confiança: -0.6331917072164601

Segmento 13:
  Início: 26.0s
  Fim: 28.0s
  Texto:  E já está morrido, já, cara. É muita novidade.
  Confiança: -0.6331917072164601

Segmento 14:
  Início: 28.0s
  Fim: 34.0s
  Texto:  Vamos que vamos, porque, cara, tem bastante coisa hoje para a gente ver também, tá?
  Confiança: -0.2604537353515625

Segmento 15:
  Início: 34.0s
  Fim: 38.0s
  Texto:  Aproveitando da Boas Vindas e Boa Noite aí para todo mundo.
  Confiança: -0.2604537353515625

Segmento 16:
  Início: 38.0s
  Fim: 42.0s
  Texto:  Tanto que a galera que está aqui nos 1, está entrando agora aqui.
  Confiança: -0.2604537353515625

Segmento 17:
  Início: 42.0s
  Fim: 45.0s
  Texto:  Tem uma galera também que está no YouTube, tá?
  Confiança: -0.2604537353515625

Segmento 18:
  Início: 45.0s
  Fim: 47.0s
  Texto:  Então, galera, sejam muito bem-vindos aí.
  Confiança: -0.2604537353515625

Segmento 19:
  Início: 47.0s
  Fim: 51.0s
  Texto:  Hoje nós estamos aí no nosso terceiro dia, né, cara.
  Confiança: -0.2604537353515625

Segmento 20:
  Início: 51.0s
  Fim: 54.0s
  Texto:  Da Fussai ou TechWeek com o IA.
  Confiança: -0.2604537353515625

Segmento 21:
  Início: 54.0s
  Fim: 60.0s
  Texto:  E eu tenho que admitir para vocês que tem bastante coisa para a gente ver,
  Confiança: -0.15197659353924614

Segmento 22:
  Início: 60.0s
  Fim: 63.0s
  Texto:  mas eu acho que a gente avançou bastante aí nesses 2 dias.
  Confiança: -0.15197659353924614

Segmento 23:
  Início: 63.0s
  Fim: 68.0s
  Texto:  Eu não sei se todo mundo aí participou, né, está participando aí com o corda com isso,
  Confiança: -0.15197659353924614

Segmento 24:
  Início: 68.0s
  Fim: 72.0s
  Texto:  mas tem bastante conteúdo que a gente viu, né.
  Confiança: -0.15197659353924614

Segmento 25:
  Início: 72.0s
  Fim: 78.0s
  Texto:  É interessante que muitos desses conteúdos e muita coisa que a gente acaba falando bastante.
  Confiança: -0.15197659353924614

Segmento 26:
  Início: 78.0s
  Fim: 83.0s
  Texto:  Não são coisas necessariamente ali no topo da pirâmide, né, no código.
  Confiança: -0.15197659353924614

Segmento 27:
  Início: 83.0s
  Fim: 90.0s
  Texto:  É o que está fazendo, vamos dizer assim, a fundação para que essa parada aconteça, né.
  Confiança: -0.1613318125406901

Segmento 28:
  Início: 90.0s
  Fim: 97.0s
  Texto:  Então, eu acho que nossa função daqui para frente, como desenvolvedores, de fato,
  Confiança: -0.1613318125406901

Segmento 29:
  Início: 97.0s
  Fim: 104.0s
  Texto:  é a gente ter esse domínio muito forte desde, obviamente, da linguagem,
  Confiança: -0.1613318125406901

Segmento 30:
  Início: 104.0s
  Fim: 107.0s
  Texto:  que é o programa do Framework que você trabalha,
  Confiança: -0.1613318125406901

Segmento 31:
  Início: 107.0s
  Fim: 112.0s
  Texto:  mas fundamentalmente em relação a parte de arquitetura de software, arquitetura de solução.
  Confiança: -0.1613318125406901

Segmento 32:
  Início: 112.0s
  Fim: 117.0s
  Texto:  E saber, obviamente, pilotar e inteligência artificial, né.
  Confiança: -0.12585094739805977

Segmento 33:
  Início: 117.0s
  Fim: 120.0s
  Texto:  Então, assim, não é trivial, né.
  Confiança: -0.12585094739805977

Segmento 34:
  Início: 120.0s
  Fim: 126.0s
  Texto:  E por isso que eu acredito que muita gente hoje em dia ainda passa muita raiva com IA,
  Confiança: -0.12585094739805977

Segmento 35:
  Início: 126.0s
  Fim: 131.0s
  Texto:  exatamente pelo fato de não ter encontrado um workflow certo,
  Confiança: -0.12585094739805977

Segmento 36:
  Início: 131.0s
  Fim: 136.0s
  Texto:  não ter conseguido entender direito toda essa parte de prompt engineer e tudo mais.
  Confiança: -0.12585094739805977

Segmento 37:
  Início: 136.0s
  Fim: 141.0s
  Texto:  Então, tem bastante coisa envolvida para você desenvolver, vamos dizer assim, né.
  Confiança: -0.12585094739805977

Segmento 38:
  Início: 141.0s
  Fim: 144.0s
  Texto:  É muito louco, né, falar que a gente vai desenvolver softwares modernos.
  Confiança: -0.13355553455841848

Segmento 39:
  Início: 144.0s
  Fim: 149.0s
  Texto:  Eu prefiro dizer que é uma nova geração de softwares, que são softwares diferentes
  Confiança: -0.13355553455841848

Segmento 40:
  Início: 149.0s
  Fim: 154.0s
  Texto:  do que a gente tem acostumado a desenvolver nos últimos 30 anos, vamos dizer assim,
  Confiança: -0.13355553455841848

Segmento 41:
  Início: 154.0s
  Fim: 156.0s
  Texto:  a coisa mudou bastante, né.
  Confiança: -0.13355553455841848

Segmento 42:
  Início: 156.0s
  Fim: 159.0s
  Texto:  O que eu normalmente começo sempre falando é,
  Confiança: -0.13355553455841848

Segmento 43:
  Início: 159.0s
  Fim: 170.0s
  Texto:  quase 30 anos atrás, a gente tinha o Game of War citando, botando ali para a gente os design patterns.
  Confiança: -0.13355553455841848

Segmento 44:
  Início: 170.0s
  Fim: 172.0s
  Texto:  E esses patterns são usados até hoje.
  Confiança: -0.21453142541600026

Segmento 45:
  Início: 172.0s
  Fim: 177.0s
  Texto:  O negócio está ali, claro, né, preto no branco que é o que não é independente
  Confiança: -0.21453142541600026

Segmento 46:
  Início: 177.0s
  Fim: 179.0s
  Texto:  se você use, se você gosta ou não.
  Confiança: -0.21453142541600026

Segmento 47:
  Início: 179.0s
  Fim: 183.0s
  Texto:  Agora, a turma que está acontecendo com IA, aconteceu agora nos últimos 3 anos
  Confiança: -0.21453142541600026

Segmento 48:
  Início: 183.0s
  Fim: 188.0s
  Texto:  do mundo de desenvolvimento, e nesse último 6 meses, então, a coisa ainda ela começou
  Confiança: -0.21453142541600026

Segmento 49:
  Início: 188.0s
  Fim: 191.0s
  Texto:  a crescer muito mais forte ainda, né.
  Confiança: -0.21453142541600026

Segmento 50:
  Início: 191.0s
  Fim: 197.0s
  Texto:  Então, o nosso objetivo aqui é a gente conseguir percorrer esse ecossistema a inteiro, né.
  Confiança: -0.21453142541600026

Segmento 51:
  Início: 197.0s
  Fim: 200.0s
  Texto:  Então, somente, pra quem está chegando hoje, galera.
  Confiança: -0.15874873467211453

Segmento 52:
  Início: 200.0s
  Fim: 206.0s
  Texto:  A gente passou 3 dias, eu vou compartilhar aqui a minha, a tela aqui do meu computador
  Confiança: -0.15874873467211453

Segmento 53:
  Início: 206.0s
  Fim: 209.0s
  Texto:  pra ficar um pouco mais claro, né.
  Confiança: -0.15874873467211453

Segmento 54:
  Início: 209.0s
  Fim: 216.0s
  Texto:  Até onde a gente tava, onde a gente parou, pra onde que a gente vai hoje, o que a gente vê,
  Confiança: -0.15874873467211453

Segmento 55:
  Início: 216.0s
  Fim: 223.0s
  Texto:  pra que vocês consigam aí se contextualizar e também relembrar um pouquinho do que a gente falou, tá.
  Confiança: -0.15874873467211453

Segmento 56:
  Início: 223.0s
  Fim: 229.0s
  Texto:  Porque é bastante informação mesmo, mas eu acredito que vale a pena, né,
  Confiança: -0.15757622718811035

Segmento 57:
  Início: 229.0s
  Fim: 234.0s
  Texto:  pelo menos vocês terem essa visão um pouco mais clara do que é possível
  Confiança: -0.15757622718811035

Segmento 58:
  Início: 234.0s
  Fim: 238.0s
  Texto:  e do que está mudando em relação à nossa profissão com IA, que tá...
  Confiança: -0.15757622718811035

Segmento 59:
  Início: 238.0s
  Fim: 243.0s
  Texto:  Que é muito além, no final das contas, de saber programar usando o cursor
  Confiança: -0.15757622718811035

Segmento 60:
  Início: 243.0s
  Fim: 247.0s
  Texto:  ou qualquer coisa desse tipo. Isso aí é só... É a ponta do iceberg.
  Confiança: -0.15757622718811035

Segmento 61:
  Início: 247.0s
  Fim: 251.0s
  Texto:  Isso aí já... Em grandes empresas, já é meio que ponto pacífico.
  Confiança: -0.15757622718811035

Segmento 62:
  Início: 251.0s
  Fim: 254.0s
  Texto:  Vamos dizer assim, aí nos dias de hoje, tá.
  Confiança: -0.2040741547294285

Segmento 63:
  Início: 254.0s
  Fim: 257.0s
  Texto:  Eu só tô subindo aqui, né, todo o material que a gente acabou vendo
  Confiança: -0.2040741547294285

Segmento 64:
  Início: 257.0s
  Fim: 266.0s
  Texto:  nesses últimos dias e na nossa agenda, basicamente, tava a gente consegui ver esses pontos aqui, tá.
  Confiança: -0.2040741547294285

Segmento 65:
  Início: 266.0s
  Fim: 270.0s
  Texto:  O ponto de partida de A pra desenvolvedores, falamos sobre a arquitetura de software,
  Confiança: -0.2040741547294285

Segmento 66:
  Início: 270.0s
  Fim: 275.0s
  Texto:  solução na área de A, a gente falou sobre conta extendinia, a gente fala sobre pronta indinia,
  Confiança: -0.2040741547294285

Segmento 67:
  Início: 275.0s
  Fim: 280.0s
  Texto:  a gente falou bastante de workflow pra desenvolvimento, ontem a gente brincou bastante com isso, né.
  Confiança: -0.2040741547294285

Segmento 68:
  Início: 280.0s
  Fim: 287.0s
  Texto:  A gente falou sobre MCP, como ferramenta que a gente pode utilizar no dia a dia,
  Confiança: -0.16426060325221012

Segmento 69:
  Início: 287.0s
  Fim: 293.0s
  Texto:  mas também a gente falou de MCP como eu poder desenvolver o meu próprio servidor MCP
  Confiança: -0.16426060325221012

Segmento 70:
  Início: 293.0s
  Fim: 300.0s
  Texto:  para que eu possa... Para que inteligências artificiais possam fazer consultas nos meus sistemas
  Confiança: -0.16426060325221012

Segmento 71:
  Início: 300.0s
  Fim: 306.0s
  Texto:  e tudo mais. Então a gente acabou dando exemplos, a gente falou sobre tools, a gente falou sobre resources,
  Confiança: -0.16426060325221012

Segmento 72:
  Início: 306.0s
  Fim: 312.0s
  Texto:  a gente falou sobre prontes, a gente deu exemplo usando até o próprio cloud,
  Confiança: -0.11641966855084454

Segmento 73:
  Início: 312.0s
  Fim: 317.0s
  Texto:  pedindo pra gerar propostas coisas extremamente automatizadas,
  Confiança: -0.11641966855084454

Segmento 74:
  Início: 317.0s
  Fim: 321.0s
  Texto:  simplesmente utilizando servidores MCPs, tá.
  Confiança: -0.11641966855084454

Segmento 75:
  Início: 321.0s
  Fim: 326.0s
  Texto:  Então o grande ponto aí, pessoal, é que o nosso objetivo hoje,
  Confiança: -0.11641966855084454

Segmento 76:
  Início: 326.0s
  Fim: 329.0s
  Texto:  agora, eu acho que é um dos dias mais bacanas, tá.
  Confiança: -0.11641966855084454

Segmento 77:
  Início: 329.0s
  Fim: 334.0s
  Texto:  Por que que eu tô dizendo isso? Porque é um dia que, além de a gente ver código,
  Confiança: -0.11641966855084454

Segmento 78:
  Início: 334.0s
  Fim: 339.0s
  Texto:  é um dia que a gente vai entender conceitos novos principalmente
  Confiança: -0.12930773744488708

Segmento 79:
  Início: 339.0s
  Fim: 342.0s
  Texto:  quando a gente tá falando em agentes inteligentes artificial,
  Confiança: -0.12930773744488708

Segmento 80:
  Início: 342.0s
  Fim: 347.0s
  Texto:  que na real, grande parte da galera que eu acabo conversando,
  Confiança: -0.12930773744488708

Segmento 81:
  Início: 347.0s
  Fim: 352.0s
  Texto:  acaba não sabendo definir necessariamente o que é um agente.
  Confiança: -0.12930773744488708

Segmento 82:
  Início: 352.0s
  Fim: 354.0s
  Texto:  Escreva um aí no chat, galera.
  Confiança: -0.12930773744488708

Segmento 83:
  Início: 354.0s
  Fim: 362.0s
  Texto:  Quem aqui, no final das contas, mangia do que é um agente realmente inteligente artificial?
  Confiança: -0.12930773744488708

Segmento 84:
  Início: 362.0s
  Fim: 368.0s
  Texto:  Se você tiver que definir, né. Então isso aí é um dos pontos que a gente vai focar bastante.
  Confiança: -0.21762891401324355

Segmento 85:
  Início: 368.0s
  Fim: 374.0s
  Texto:  Então o nosso objetivo hoje é a gente falar de REG, que é retruívalmente a generation.
  Confiança: -0.21762891401324355

Segmento 86:
  Início: 374.0s
  Fim: 380.0s
  Texto:  A gente vai falar um pouco sobre bancos de dados, principalmente bancos de dados vetoriais.
  Confiança: -0.21762891401324355

Segmento 87:
  Início: 380.0s
  Fim: 386.0s
  Texto:  Aí a gente vai entrar e realmente entender o que é um agente diá de verdade.
  Confiança: -0.21762891401324355

Segmento 88:
  Início: 386.0s
  Fim: 391.0s
  Texto:  A gente vai entender diferenças de agentes de A pra automações.
  Confiança: -0.21762891401324355

Segmento 89:
  Início: 391.0s
  Fim: 397.0s
  Texto:  Tem gente que cria uma automação no N8N e fala que cria um agente.
  Confiança: -0.15484717796588765

Segmento 90:
  Início: 397.0s
  Fim: 402.0s
  Texto:  A gente vai falar sobre aplicações multiagenteicas, ou seja,
  Confiança: -0.15484717796588765

Segmento 91:
  Início: 402.0s
  Fim: 407.0s
  Texto:  como que eu cria uma aplicação que tem diversos agentes se comunicando com os outros para resolver os problemas.
  Confiança: -0.15484717796588765

Segmento 92:
  Início: 407.0s
  Fim: 414.0s
  Texto:  A gente vai falar sobre os frameworks que são populares hoje em dia para trabalhar com desenvolvimento de agentes.
  Confiança: -0.15484717796588765

Segmento 93:
  Início: 414.0s
  Fim: 418.0s
  Texto:  A gente vai falar sobre design patterns, a gente vai falar sobre boas práticas,
  Confiança: -0.15484717796588765

Segmento 94:
  Início: 418.0s
  Fim: 424.0s
  Texto:  a gente vai falar sobre guard rails, que são formas e a gente tentar minimamente proteger o escopo
  Confiança: -0.1476577380756

Segmento 95:
  Início: 424.0s
  Fim: 428.0s
  Texto:  e o que vai acontecer com o comportamento dos nossos agentes.
  Confiança: -0.1476577380756

Segmento 96:
  Início: 428.0s
  Fim: 437.0s
  Texto:  E a gente vai falar também um pouco de evaluation para como que a gente consegue garantir que os resultados das conversas com os meus agentes,
  Confiança: -0.1476577380756

Segmento 97:
  Início: 437.0s
  Fim: 442.0s
  Texto:  com os sistemas meus que integram com inteligência artificial, eles não estão delirando
  Confiança: -0.1476577380756

Segmento 98:
  Início: 442.0s
  Fim: 446.0s
  Texto:  e vão fazer besteira quando eles chegarem ao cliente final.
  Confiança: -0.1476577380756

Segmento 99:
  Início: 446.0s
  Fim: 451.0s
  Texto:  Então, a grande sacada aqui é que a gente tem bastante conteúdo para ver,
  Confiança: -0.1538059943545181

Segmento 100:
  Início: 451.0s
  Fim: 455.0s
  Texto:  mas eu vou mostrar exemplos para vocês de agentes funcionando, eu vou mostrar códigos de agentes,
  Confiança: -0.1538059943545181

Segmento 101:
  Início: 455.0s
  Fim: 458.0s
  Texto:  eu vou trazer os conceitos de realmente o que é o que não é um agente.
  Confiança: -0.1538059943545181

Segmento 102:
  Início: 458.0s
  Fim: 462.0s
  Texto:  Então, essas coisas vão ficar bem bacanas aí na hora que a gente está trabalhando.
  Confiança: -0.1538059943545181

Segmento 103:
  Início: 462.0s
  Fim: 466.0s
  Texto:  Fechou? Galera.
  Confiança: -0.1538059943545181

Segmento 104:
  Início: 466.0s
  Fim: 470.0s
  Texto:  E somente para a gente relembrar algumas coisinhas.
  Confiança: -0.1538059943545181

Segmento 105:
  Início: 470.0s
  Fim: 477.0s
  Texto:  A gente vai emitir um certificado pela faculdade do Fulsaico de Tecnologia, a FCT, a nossa faculdade,
  Confiança: -0.23755817060117368

Segmento 106:
  Início: 477.0s
  Fim: 481.0s
  Texto:  para quem participou dos três dias de evento do início ao fim.
  Confiança: -0.23755817060117368

Segmento 107:
  Início: 481.0s
  Fim: 488.0s
  Texto:  Então, no final de cada dia, a gente passou, já no dia 1 e dia 2, uma lista de presença.
  Confiança: -0.23755817060117368

Segmento 108:
  Início: 488.0s
  Fim: 496.0s
  Texto:  E quem tiver nessas três listas de presença, vai receber a emissão de um certificado pela nossa faculdade.
  Confiança: -0.23755817060117368

Segmento 109:
  Início: 496.0s
  Fim: 504.0s
  Texto:  Então, somente para que vocês saibam, normalmente, no final do evento, a gente passa essa lista.
  Confiança: -0.170616455078125

Segmento 110:
  Início: 504.0s
  Fim: 510.0s
  Texto:  E depois, a gente desabilita o link, quem participou dos três dias, vai levar o certificado.
  Confiança: -0.170616455078125

Segmento 111:
  Início: 510.0s
  Fim: 514.0s
  Texto:  Maravilha? Chou de bola, pessoal.
  Confiança: -0.170616455078125

Segmento 112:
  Início: 514.0s
  Fim: 518.0s
  Texto:  Então, essa é a grande sacada de tudo.
  Confiança: -0.170616455078125

Segmento 113:
  Início: 518.0s
  Fim: 523.0s
  Texto:  E eu quero já ir direto ao assunto, somente para lembrar, pessoal,
  Confiança: -0.170616455078125

Segmento 114:
  Início: 523.0s
  Fim: 528.0s
  Texto:  quando a gente está falando inteligência artificial para desenvolvedores, a gente está falando
  Confiança: -0.17884465759875728

Segmento 115:
  Início: 528.0s
  Fim: 536.0s
  Texto:  dessa camada aqui, onde nós desenvolvedores somos usuários de modelos de inteligência artificial.
  Confiança: -0.17884465759875728

Segmento 116:
  Início: 536.0s
  Fim: 542.0s
  Texto:  Nosso foco aqui não é para discutir Machine Learning, não é para discutir treinamento de modelos e coisas desse tipo.
  Confiança: -0.17884465759875728

Segmento 117:
  Início: 542.0s
  Fim: 550.0s
  Texto:  Nosso ponto aqui está mesmo em desenvolvimento de software, que vai se integrar com a e desenvolvimento de software.
  Confiança: -0.17884465759875728

Segmento 118:
  Início: 551.0s
  Fim: 558.0s
  Texto:  Como que a gente usa ia para ser mais produtivo? Então, é basicamente esse é o nosso foco aqui nesses três dias.
  Confiança: -0.2857952117919922

Segmento 119:
  Início: 558.0s
  Fim: 563.0s
  Texto:  Então, esse aí, o ponto importante para todo mundo semplificar a ciência.
  Confiança: -0.2857952117919922

Segmento 120:
  Início: 563.0s
  Fim: 576.0s
  Texto:  Pessoal, eu vou continuar, onde que eu parei, ontem, para quem não pode assistir, os vídeos estão disponíveis no YouTube ainda.
  Confiança: -0.2857952117919922

Segmento 121:
  Início: 576.0s
  Fim: 584.0s
  Texto:  Depois, com o evento acabar, nós vamos tirar esses vídeos e eles vão ficar disponíveis somente para quem são alunos da Fussaiq.
  Confiança: -0.2249692141354739

Segmento 122:
  Início: 584.0s
  Fim: 590.0s
  Texto:  Então, somente para lembrar também você na Fussaiq, a gente tem cursos.
  Confiança: -0.2249692141354739

Segmento 123:
  Início: 590.0s
  Fim: 601.0s
  Texto:  E também, eu não posso deixar de dizer que nós temos também um MBA, um MBA focado em engenharia de software com a.
  Confiança: -0.2249692141354739

Segmento 124:
  Início: 601.0s
  Fim: 611.0s
  Texto:  Então, se você quer aprender realmente a, tem um workflow decente para você desenvolver ser mais produtivo, de verdade, independente do tipo e o tamanho do projeto.
  Confiança: -0.16593225540653353

Segmento 125:
  Início: 611.0s
  Fim: 621.0s
  Texto:  Se você quer aprender realmente o que está por trás dos novos momentos no mundo da arquitetura de software de solução, para você criar realmente aplicações de grande porte,
  Confiança: -0.16593225540653353

Segmento 126:
  Início: 621.0s
  Fim: 624.0s
  Texto:  para trabalhar realmente em presa grande, tudo mais.
  Confiança: -0.16593225540653353

Segmento 127:
  Início: 624.0s
  Fim: 632.0s
  Texto:  E também, se você quer aprender a desenvolver a gente, de integrar aplicações com e a fazer entregas, deplage dessas aplicações,
  Confiança: -0.2920822567409939

Segmento 128:
  Início: 632.0s
  Fim: 644.0s
  Texto:  esse MBA em engenharia de software, ele foi feito para você, está somente para você saberem, você pode entrar, clicar aqui, solicitar um contato e bater um papo com a nossa equipe, tá?
  Confiança: -0.2920822567409939

Segmento 129:
  Início: 644.0s
  Fim: 656.0s
  Texto:  Porque a gente tem turmas que abrem em momentos diferentes, então a gente tem uma próxima turma que vai abrir no final de setembro, tá?
  Confiança: -0.11986186871161827

Segmento 130:
  Início: 656.0s
  Fim: 663.0s
  Texto:  Agora, dia 29, mas a gente já tem turma para dezembro, então dependendo da sua intenção, né?
  Confiança: -0.11986186871161827

Segmento 131:
  Início: 663.0s
  Fim: 673.0s
  Texto:  Dependendo da situação, a gente consegue fazer negociações um pouco diferentes em relação a facilidade de pagamento, formas de pagamento e coisas desse tipo, tá?
  Confiança: -0.11986186871161827

Segmento 132:
  Início: 673.0s
  Fim: 683.0s
  Texto:  Então, se você tiver com interesse, dessa chance clica, solicito o contato, se vai aprender nome e meio, a gente entra em contato, fala, traz todas as possibilidades para você,
  Confiança: -0.174954833984375

Segmento 133:
  Início: 683.0s
  Fim: 690.0s
  Texto:  independente se você quer começar agora, ou se planejar um pouco mais para o final do ano, eventualmente tendo algum benefício aí para vocês.
  Confiança: -0.174954833984375

Segmento 134:
  Início: 690.0s
  Fim: 701.0s
  Texto:  Fechou? Então, essa aqui é a grande sacada, a gente vai fazer sub-lots, então os valores eles vão subir no progressivamente, então a minha dica aqui é,
  Confiança: -0.174954833984375

Segmento 135:
  Início: 701.0s
  Fim: 708.0s
  Texto:  entra em contato com a gente, vamos bater esse papo, principalmente nessa semana, vou pedir aí para a minha equipe, por favor,
  Confiança: -0.16268494423855556

Segmento 136:
  Início: 708.0s
  Fim: 713.0s
  Texto:  colocar o link e aí tanto nos 1 quanto no YouTube aí para vocês. Fechou?
  Confiança: -0.16268494423855556

Segmento 137:
  Início: 713.0s
  Fim: 720.0s
  Texto:  Maravilha, galera, lembrando que é um MBA reconhecido pelo MAC, certificado pela faculdade e tudo mais. Maravilha?
  Confiança: -0.16268494423855556

Segmento 138:
  Início: 720.0s
  Fim: 732.0s
  Texto:  Pessoal, vamos agora, então, focar aqui, da onde a gente parou, né? Ontem a gente terminou basicamente falando sobre Modo e Context Protocol,
  Confiança: -0.294553279876709

Segmento 139:
  Início: 732.0s
  Fim: 741.0s
  Texto:  ou MCP, que façam que a gente tem um novo protocolo aí para que a gente consiga fazer integrações aí com inteligência artificial, tá?
  Confiança: -0.294553279876709

Segmento 140:
  Início: 741.0s
  Fim: 751.0s
  Texto:  O lance aqui é o seguinte, pessoal, nós vamos entrar em agora num momento diferente, porque até agora a gente falou muito de produtividade,
  Confiança: -0.11462061545428108

Segmento 141:
  Início: 751.0s
  Fim: 762.0s
  Texto:  a gente deu exemplos e pontos, inclusive de arquiteturas e coisas desse tipo, mas tem algo bem importante aqui, no meio da história, tá?
  Confiança: -0.11462061545428108

Segmento 142:
  Início: 762.0s
  Fim: 772.0s
  Texto:  Que é o quê? Estratégias e até formas que, inevitavelmente, você vai ter que fazer para trabalhar com inteligência artificial,
  Confiança: -0.1492498732104744

Segmento 143:
  Início: 772.0s
  Fim: 778.0s
  Texto:  quando você está trabalhando com as suas aplicações. Eu vou dar um exemplo aqui para você, tá?
  Confiança: -0.1492498732104744

Segmento 144:
  Início: 778.0s
  Fim: 791.0s
  Texto:  Vamos imaginar que você tem aí a sua empresa, e, de repente, a sua empresa ela quer fazer, por exemplo, uma área de atendimento de suporte.
  Confiança: -0.1492498732104744

Segmento 145:
  Início: 791.0s
  Fim: 796.0s
  Texto:  Eu vou dar um exemplo aqui, galera, eu estou dando um exemplo assim de um domínio muito fácil para todo mundo entender,
  Confiança: -0.11426219769886561

Segmento 146:
  Início: 796.0s
  Fim: 805.0s
  Texto:  mas poderia ser trabalhar com um projeto, com um processo internos, fazer automações, revisar processos, poderia ser qualquer coisa,
  Confiança: -0.11426219769886561

Segmento 147:
  Início: 805.0s
  Fim: 818.0s
  Texto:  mas eu estou dando um exemplo que fique bem fácil para todo mundo entender. Então, vamos imaginar que você quer criar ali uma forma que o cliente final possa conversar ali com uma inteligência artificial e tirar dúvidas de suporte.
  Confiança: -0.11426219769886561

Segmento 148:
  Início: 818.0s
  Fim: 830.0s
  Texto:  O que acontece? O ponto de tudo isso é que a inteligência artificial ela não tem os dados da sua empresa.
  Confiança: -0.17094737193623527

Segmento 149:
  Início: 830.0s
  Fim: 844.0s
  Texto:  Então, normalmente existem estratégias que você pode utilizar para que você consiga fornecer esses dados para que a ia responda o usuário final.
  Confiança: -0.17094737193623527

Segmento 150:
  Início: 844.0s
  Fim: 858.0s
  Texto:  Legal. E algumas dessas estratégias, elas são muito caras, algumas dessas estratégias, eventualmente elas são ineficientes, e algumas dessas estratégias a gente consegue equilibrar um pouco.
  Confiança: -0.16676095257634702

Segmento 151:
  Início: 858.0s
  Fim: 876.0s
  Texto:  Uma das formas que o mundo, antes, principalmente de ir à generativa, sempre foi acostumado, é o seguinte, você ter um modelo, e o que eu faço aqui nesse modelo?
  Confiança: -0.16680695793845438

Segmento 152:
  Início: 876.0s
  Fim: 894.0s
  Texto:  Eu pego dados que eu tenho da minha empresa, e faço uma camada de treinamento em cima desse modelo. Normalmente a gente chama isso de fine-tune. Então, fine-tune você consegue fazer isso para que o modelo ele consiga entender melhor sobre você.
  Confiança: -0.15414032103523376

Segmento 153:
  Início: 894.0s
  Fim: 908.0s
  Texto:  Para fazer isso, você precisa de muito dado. Muito dado mesmo. É custoso, e você precisa realmente trabalhar muito pesado naqueles leis de baixo, de machine learning, e tudo mais.
  Confiança: -0.2264295660931131

Segmento 154:
  Início: 908.0s
  Fim: 915.0s
  Texto:  E mesmo assim, você tem muito risco ainda de você não ter as informações totalmente acuradas.
  Confiança: -0.17257009506225585

Segmento 155:
  Início: 915.0s
  Fim: 927.0s
  Texto:  Ponto importante de tudo isso é que, no final do dia, muitos casos isso aqui é caro e às vezes é ineficiente.
  Confiança: -0.16388867322136375

Segmento 156:
  Início: 927.0s
  Fim: 941.0s
  Texto:  Eu não estou falando que você vai descartar a possibilidade de fazer fine-tune em modelos. Não estou dizendo isso. Mas existem algumas outras alternativas que podem facilitar um pouco a nossa vida. Qual que é essa alternativa?
  Confiança: -0.16388867322136375

Segmento 157:
  Início: 941.0s
  Fim: 953.0s
  Texto:  É basicamente o seguinte. Vamos imaginar que eu tenho o meu, deixa eu pegar aqui, que eu acho que fica até mais fácil para eu mostrar, que é algo se você pensar básico, se você pensar dessa forma.
  Confiança: -0.12282046886405559

Segmento 158:
  Início: 953.0s
  Fim: 970.0s
  Texto:  Eu tenho um usuário, ele consulta o sistema, o sistema consulta o banco de dados e responde para o usuário. Quem aí tem necessariamente alguma familiaridade com isso que eu acabei de mostrar para vocês?
  Confiança: -0.12282046886405559

Segmento 159:
  Início: 970.0s
  Fim: 992.0s
  Texto:  Em pessoal, todo mundo, com a IA hoje em dia, a gente também faz isso. Mas nós fazemos isso de uma forma bem diferente. E qual que é a forma diferente que nós fazemos? Nós temos que usar algumas estratégias.
  Confiança: -0.1955047607421875

Segmento 160:
  Início: 992.0s
  Fim: 1009.0s
  Texto:  Uma estratégia muito conhecida é o seguinte. Eu vou, o usuário vai bater no sistema e quando eu falo usuário, pode ser usuário final, pode ser um outro sistema, pode ser um micro serviço, pode ser o que for.
  Confiança: -0.12177196301912006

Segmento 161:
  Início: 1009.0s
  Fim: 1025.0s
  Texto:  E pessoal, entendam que existe uma diferença enorme entre chatbot e uma gente de A. A gente vai falar sobre isso com mais tarde, mas não pense que qualquer coisa de conversação é um chatbot, porque não é.
  Confiança: -0.19808556051815257

Segmento 162:
  Início: 1026.0s
  Fim: 1040.0s
  Texto:  Então, o seguinte, o que eu posso fazer nesse caso para que eu consiga bater nesse sistema? Esse sistema aqui, no final das contas, ele tem um modelo de inteligência artificial.
  Confiança: -0.1322787349874323

Segmento 163:
  Início: 1040.0s
  Fim: 1069.0s
  Texto:  Então, a impute do usuário vai cair aqui na inteligência artificial. E como que a inteligência artificial vai conseguir acessar meu banco de dados para ler essas informações? Essa aí começa a ser basicamente uma das formas principais que é um desafio. Então, a gente tem esse desafio número 1. Como que o nosso LLM consegue ler os dados do nosso banco de dados?
  Confiança: -0.13872680444827026

Segmento 164:
  Início: 1070.0s
  Fim: 1082.0s
  Texto:  Segunda coisa que é um outro desafio é como que o LLM tendo acesso ao nosso banco de dados, ele vai conseguir responder ao usuário final de forma correta.
  Confiança: -0.10084676181568819

Segmento 165:
  Início: 1082.0s
  Fim: 1099.0s
  Texto:  Então, nesses nossos casos, existe uma técnica que vai funcionar mais ou menos dessa forma. Eu vou fazer o seguinte. Eu vou, no final do dia, ter algo para o meu agente
  Confiança: -0.10084676181568819

Segmento 166:
  Início: 1099.0s
  Fim: 1122.0s
  Texto:  ou para o meu sistema que eu vou consultar os dados no banco de dados. Aí eu vou pegar esses dados e montar um prąpti com ele. Como assim? Montar um prąpti com ele. Algumas ou menos do tipo? Você é especialista em suporte.
  Confiança: -0.23644724888588065

Segmento 167:
  Início: 1123.0s
  Fim: 1142.0s
  Texto:  Contesto e informações para dúvidas do cliente. E daqui eu coloco os dados vindo do banco de dados.
  Confiança: -0.10877619584401449

Segmento 168:
  Início: 1143.0s
  Fim: 1154.0s
  Texto:  E depois aqui embaixo eu coloco pergunta do cliente. E aqui eu coloco a pergunta que o cliente fez.
  Confiança: -0.17661027908325194

Segmento 169:
  Início: 1156.0s
  Fim: 1167.0s
  Texto:  E depois embaixo eu coloco responda educadamente a pergunta baseado no contexto acima.
  Confiança: -0.17661027908325194

Segmento 170:
  Início: 1168.0s
  Fim: 1187.0s
  Texto:  Então, é exatamente dessa forma que grande parte dos sistemas que trabalham com inteligência artificial trabalha. Eles recuperam dados no banco de dados, pego essas informações e concatenam com o prąpti.
  Confiança: -0.1418000046087771

Segmento 171:
  Início: 1187.0s
  Fim: 1204.0s
  Texto:  E aí, o que acontece? Quando o cliente perguntar como que eu abro um tópico no forum, aqui no banco de dados vai ter informação para ir a como se abre um tópico no forum. Aí vai ler isso aqui e vai responder. Porque ela sabe porque a informação que ela precisa está logo em cima.
  Confiança: -0.22177823384602866

Segmento 172:
  Início: 1204.0s
  Fim: 1221.0s
  Texto:  Fez sentido isso para vocês? Então assim, não há nada de mágicos se você perceber. Eu estou fazendo algo óbvio. É mais ou menos assim. Imagina que você não sabe quanto quer um mais um.
  Confiança: -0.195525562061983

Segmento 173:
  Início: 1222.0s
  Fim: 1227.0s
  Texto:  E você tem que responder alguém. Aí você recebe uma folha na sua frente.
  Confiança: -0.11783080630832249

Segmento 174:
  Início: 1228.0s
  Fim: 1233.0s
  Texto:  Wesley, você vai ensinar para as pessoas como fazer contas.
  Confiança: -0.11783080630832249

Segmento 175:
  Início: 1234.0s
  Fim: 1241.0s
  Texto:  O resultado de um mais um é dois. A pergunta que alguém fez agora é qual o resultado de um mais um?
  Confiança: -0.11783080630832249

Segmento 176:
  Início: 1242.0s
  Fim: 1250.0s
  Texto:  Aí eu vou chegar e falar pessoal, o resultado de um mais um é dois. Porque eu tenho a cola comigo. Você entendeu? Isso aí é um ponto importante.
  Confiança: -0.11783080630832249

Segmento 177:
  Início: 1251.0s
  Fim: 1267.0s
  Texto:  Só uma coisa que educa na real velho. Está ficando um pouco chato você flutar o chat escrevendo a paligada, tipo taligado e etc. Então por favor pare de flutar o chat. Se você continuar fludando a gente vai ficar com você. Com toda educação do mundo.
  Confiança: -0.3374475751604353

Segmento 178:
  Início: 1268.0s
  Fim: 1276.0s
  Texto:  Acaba atrapalhando ali em raciocínio minha e das pessoas que estão aí no chat. Fechou? Então pessoal, o lanço é o seguinte.
  Confiança: -0.3374475751604353

Segmento 179:
  Início: 1276.0s
  Fim: 1289.0s
  Texto:  É como se você desce pra ir a uma cola de uma prova e embaixo tais perguntas. Então ela vai olhar o que está em cima e vai responder. Fechou? Isso aí é uma forma de você fazer.
  Confiança: -0.12110635285736412

Segmento 180:
  Início: 1290.0s
  Fim: 1294.0s
  Texto:  E é uma forma que é muito utilizada. A ideia principal é essa.
  Confiança: -0.12110635285736412

Segmento 181:
  Início: 1295.0s
  Fim: 1304.0s
  Texto:  O ponto interessante que nós temos que tomar cuidado é que não existe solução simples para a problema complexo.
  Confiança: -0.12110635285736412

Segmento 182:
  Início: 1304.0s
  Fim: 1314.0s
  Texto:  E eu vou começar agora dar alguns exemplos para vocês. Vamos imaginar que eu tenho exatamente essa arquitetura que eu acabei de mostrar para vocês.
  Confiança: -0.1379655805127374

Segmento 183:
  Início: 1315.0s
  Fim: 1325.0s
  Texto:  Mas como que eu consigo no final das contas? É simplesmente pegar essas informações do banco de dados.
  Confiança: -0.1379655805127374

Segmento 184:
  Início: 1325.0s
  Fim: 1339.0s
  Texto:  Por quê? Porque quando eu estou falando de informações, elas não estão todas bonitinhas em um único banco de dados da empresa. Isso aí começa a trazer uma complicação.
  Confiança: -0.14615685145060223

Segmento 185:
  Início: 1340.0s
  Fim: 1347.0s
  Texto:  Porque se você olhar a sua empresa tem informações no site, tem um monte de PDF.
  Confiança: -0.14615685145060223

Segmento 186:
  Início: 1347.0s
  Fim: 1357.0s
  Texto:  Tem um vocobox. Um planilha do Excel. Nesses documentos, eles são usados em diversos estes textos.
  Confiança: -0.5616530849509043

Segmento 187:
  Início: 1358.0s
  Fim: 1370.0s
  Texto:  A gente tem que operar um relatório para mim para os dados do faturamento da empresa. E para isso ela precisa dar planilha do faturamento da empresa.
  Confiança: -0.5616530849509043

Segmento 188:
  Início: 1370.0s
  Fim: 1392.0s
  Texto:  Como que ia ela vai ter acesso a essa planilha? Vocês entendem que nós temos muitas fontes de dados. E como que você consegue jogar essas fontes de dados de uma forma minimamente organizada para ela conseguir fazer essa busca? Isso aí é extremamente complexo.
  Confiança: -0.12870400951754662

Segmento 189:
  Início: 1392.0s
  Fim: 1409.0s
  Texto:  Agora vamos partir do princípio que nós temos a seguinte situação. Vamos imaginar que todo o manual existe na minha empresa de suporte, por exemplo que a gente está colocando aqui embaixo, está aqui no nosso PDF.
  Confiança: -0.15371377944946288

Segmento 190:
  Início: 1409.0s
  Fim: 1420.0s
  Texto:  Eu tenho um suporte.pdf que tem toda a base de suporte que a empresa tem. E ele tem aqui mil páginas.
  Confiança: -0.17648112433297294

Segmento 191:
  Início: 1422.0s
  Fim: 1430.0s
  Texto:  Perfeito? Maravilha. Então, o que que acontece o seguinte? Se eu seguir essa linha de raciocínio que eu falei aqui para vocês,
  Confiança: -0.17648112433297294

Segmento 192:
  Início: 1430.0s
  Fim: 1438.0s
  Texto:  esse contexto de dados vindo do meu banco de dados que estão nesse PDF, vão ficar aqui embaixo.
  Confiança: -0.11280861208515783

Segmento 193:
  Início: 1439.0s
  Fim: 1454.0s
  Texto:  E eu vou botar mil páginas aqui dentro. E para quem é uma pessoa bem inteligente ou que tem um mínimo de raciocínio básico,
  Confiança: -0.11280861208515783

Segmento 194:
  Início: 1455.0s
  Fim: 1460.0s
  Texto:  consegue perceber que tem algo errado botar mil páginas dentro de um prompt da IA.
  Confiança: -0.18152785945583033

Segmento 195:
  Início: 1462.0s
  Fim: 1463.0s
  Texto:  Perfeito?
  Confiança: -0.18152785945583033

Segmento 196:
  Início: 1466.0s
  Fim: 1472.0s
  Texto:  Acredito que vocês consigam entender que isso de forma geral não é legal.
  Confiança: -0.18152785945583033

Segmento 197:
  Início: 1473.0s
  Fim: 1483.0s
  Texto:  E não é legal por muitos motivos. Porque às vezes, o usuário está perguntando algo que está definido
  Confiança: -0.18152785945583033

Segmento 198:
  Início: 1483.0s
  Fim: 1488.0s
  Texto:  em um único parágrafo. E para isso, ele tem que carregar ali mil páginas.
  Confiança: -0.1422151583377446

Segmento 199:
  Início: 1489.0s
  Fim: 1499.0s
  Texto:  O outro ponto importante aqui é que você vai gastar uma furtuna de dinheiro para ir a processar esse monte de informação por conta dos tokens.
  Confiança: -0.1422151583377446

Segmento 200:
  Início: 1500.0s
  Fim: 1511.0s
  Texto:  Se você for ainda mais louco, você vai pegar todos os documentos de botar no prompt e é uma hora, o seu modelo ainda nem vai ter janela de contexto para conseguir suportar todo esse material.
  Confiança: -0.1422151583377446

Segmento 201:
  Início: 1512.0s
  Fim: 1522.0s
  Texto:  Então, basicamente, é algo que vai aumentar a latência, é algo que vai ficar mais custoso, é algo que simplesmente não faz sentido.
  Confiança: -0.14604610488528297

Segmento 202:
  Início: 1523.0s
  Fim: 1527.0s
  Texto:  Para quem é desenvolvedor, na hora que a gente fala algo desse tipo, já vai cheirar mal.
  Confiança: -0.14604610488528297

Segmento 203:
  Início: 1528.0s
  Fim: 1533.0s
  Texto:  Agora, como que nós conseguimos resolver esse tipo de problema, ou minimizar esse tipo de problema?
  Confiança: -0.14604610488528297

Segmento 204:
  Início: 1534.0s
  Fim: 1552.0s
  Texto:  O lance é o seguinte, eu posso fazer o seguinte, eu posso pegar essas minhas mil páginas e transformar essas mil páginas em mil documentos separados.
  Confiança: -0.11601059611250715

Segmento 205:
  Início: 1553.0s
  Fim: 1564.0s
  Texto:  E aí, eu vou pegar esses mil documentos separados e cada documento separado desse vai ser uma linha no banco de dados.
  Confiança: -0.35162421478622263

Segmento 206:
  Início: 1568.0s
  Fim: 1581.0s
  Texto:  Perfeito? Conseguir o capitato aí? Então, na hora que eu fizer uma pergunta como abrir um ticket de suporte, vai cair na LLM, a LLM vai consultar, a gente vai consultar com o seu dinheiro.
  Confiança: -0.35162421478622263

Segmento 207:
  Início: 1582.0s
  Fim: 1590.0s
  Texto:  Ela vai consultar o banco de dados, não é necessariamente a LLM, vai consultar o banco, ela pode usar uma tu para fazer isso, mas isso é uma outra questão.
  Confiança: -0.15957628270631197

Segmento 208:
  Início: 1590.0s
  Fim: 1595.0s
  Texto:  E ela vai fazer uma busca aqui de como criar um ticket de suporte.
  Confiança: -0.15957628270631197

Segmento 209:
  Início: 1596.0s
  Fim: 1608.0s
  Texto:  O problema no final das contas é como que a gente consegue fazer uma busca que vá trazer para a gente desses mil documentos separados,
  Confiança: -0.15957628270631197

Segmento 210:
  Início: 1608.0s
  Fim: 1620.0s
  Texto:  informações realmente relevantes em relação à pergunta do usuário.
  Confiança: -0.14101403951644897

Segmento 211:
  Início: 1620.0s
  Fim: 1632.0s
  Texto:  Porque às vezes uma pergunta como abrir um ticket de suporte pode ser simples, mas eu posso perguntar como que funciona o processo interno de suporte para o treino a meus funcionários, para que eles consigam.
  Confiança: -0.15127210719611056

Segmento 212:
  Início: 1632.0s
  Fim: 1645.0s
  Texto:  A abrir um ticket é ao mesmo tempo, eles conseguirem fazer essa resposta, eles entenderem um pouco melhor o passo a passo de como responder e uma guide line do que poder falar ou não.
  Confiança: -0.15127210719611056

Segmento 213:
  Início: 1646.0s
  Fim: 1659.0s
  Texto:  Você concorda comigo que na hora que a gente faz e joga essa pergunta para ir a como ela vai conseguir pegar essa pergunta que às vezes é mais do que uma pergunta e buscar isso no banco de dados.
  Confiança: -0.1406658093134562

Segmento 214:
  Início: 1660.0s
  Fim: 1675.0s
  Texto:  Então, existe uma forma de fazer isso. Essa forma que normalmente a gente faz é o seguinte, nós pegamos essas mil páginas e realmente nós separamos em mil documentos.
  Confiança: -0.14615686734517416

Segmento 215:
  Início: 1676.0s
  Fim: 1687.0s
  Texto:  E quando eu falo em mil documentos eu escolho a quantidade de tokens ou caractérios que eu quero separar. Isso a gente chama de splitter.
  Confiança: -0.15649914495723763

Segmento 216:
  Início: 1687.0s
  Fim: 1692.0s
  Texto:  A gente vai fazer um splitter dessas informações e a gente vai ter mil documentos.
  Confiança: -0.15649914495723763

Segmento 217:
  Início: 1692.0s
  Fim: 1703.0s
  Texto:  Obviamente esses mil documentos vão ter que parar nesse banco de dados, mas o grande problema é como que eu faço a busca para eu trazer os documentos mais relevantes.
  Confiança: -0.15649914495723763

Segmento 218:
  Início: 1703.0s
  Fim: 1723.0s
  Texto:  Então, nesse caso, o que a gente pode fazer é o seguinte, nós podemos pegar um modelo de embedding que, no final das contas, ele funciona basicamente com o seguinte. Você vai pegar o seu texto, o seu conteúdo,
  Confiança: -0.13824818752430104

Segmento 219:
  Início: 1723.0s
  Fim: 1731.0s
  Texto:  ele vai transformar esse texto num vetor.
  Confiança: -0.28469085693359375

Segmento 220:
  Início: 1731.0s
  Fim: 1751.0s
  Texto:  E a divinha só, esses dados vão parar no banco de dados. Então, eu vou ter aqui o meu conteúdo puro explicando como faz a coisa e eu vou ter o vetor desse conteúdo.
  Confiança: -0.28469085693359375

Segmento 221:
  Início: 1751.0s
  Fim: 1775.0s
  Texto:  Quando alguém fizer uma pergunta para mim, o que eu vou fazer? Eu vou pegar a pergunta do usuário, transformar em vetor e vou fazer uma busca no banco de dados das informações vetorizadas.
  Confiança: -0.09653369585673015

Segmento 222:
  Início: 1776.0s
  Fim: 1782.0s
  Texto:  Fê sentido para vocês, galera, o que eu estou falando? O passo a passo de como isso funciona?
  Confiança: -0.14256647500124844

Segmento 223:
  Início: 1782.0s
  Fim: 1788.0s
  Texto:  A mesma para quem nunca ouviu falar sobre isso, então isso a gente faz algo que a gente chama de busca semântica.
  Confiança: -0.14256647500124844

Segmento 224:
  Início: 1788.0s
  Fim: 1799.0s
  Texto:  Ele consegue, basicamente, pegar o vetor da pergunta, comparar com todos os vetores que estão registrados do banco de dados
  Confiança: -0.14256647500124844

Segmento 225:
  Início: 1799.0s
  Fim: 1811.0s
  Texto:  e trazer para mim alguns registros que tenham mais sentido em relação àquela pergunta. É basicamente isso o que acontece.
  Confiança: -0.1354177422719459

Segmento 226:
  Início: 1811.0s
  Fim: 1825.0s
  Texto:  E uma vez que eu tenho esses trechos dessas informações, eu vou colocar essas informações aqui e eu vou responder o cliente à invés de eu carregar mil páginas aqui para a gente.
  Confiança: -0.1354177422719459

Segmento 227:
  Início: 1825.0s
  Fim: 1842.0s
  Texto:  Legal? Então, é basicamente essa ideia crua de como que funciona essa estratégia. Essa estratégia é muito utilizada e ela é chamada de regge.
  Confiança: -0.22632870307335487

Segmento 228:
  Início: 1842.0s
  Fim: 1854.0s
  Texto:  Então, regge, stands for, stands for, regge significa, deixa eu colar aqui, retrieval aumenta de generation.
  Confiança: -0.261567461317864

Segmento 229:
  Início: 1854.0s
  Fim: 1862.0s
  Texto:  Então, a invés de eu ficar fazendo fine-tuning no modelo, eu ingeto a informação que eu quero com o modelo, ele responda aqui para mim.
  Confiança: -0.261567461317864

Segmento 230:
  Início: 1862.0s
  Fim: 1888.0s
  Texto:  De forma grosseira funciona dessa forma como que você vai trabalhar com regge. A maior dificuldade pessoal é que mesmo você fazendo essas buscas vetoriais, não necessariamente o conteúdo que você vai receber vai resolver a dor do cliente.
  Confiança: -0.18220378734447337

Segmento 231:
  Início: 1889.0s
  Fim: 1907.0s
  Texto:  Então, a gente pode ter algumas situações bem simples. Então, vamos imaginar o seguinte. Vamos imaginar que eu tenho um documento que eu falo o seguinte no documento, alguma coisa seguinte.
  Confiança: -0.1306683752271864

Segmento 232:
  Início: 1908.0s
  Fim: 1916.0s
  Texto:  Nem um cliente pode ter desconto de 50%.
  Confiança: -0.19586411275361715

Segmento 233:
  Início: 1916.0s
  Fim: 1927.0s
  Texto:  Beleza? Vamos imaginar agora que esse conteúdo aqui está junto com um monte de outro conteúdo aqui.
  Confiança: -0.19586411275361715

Segmento 234:
  Início: 1927.0s
  Fim: 1940.0s
  Texto:  Um monte de outros informações. Aqui tem conteúdo para caramba, lias em cima de lias aqui no meu PDF. Um monte aqui, meu manual de vendas, de como que os vendedores devem trabalhar na empresa.
  Confiança: -0.19577766109157252

Segmento 235:
  Início: 1940.0s
  Fim: 1948.0s
  Texto:  E uma dessas partes desse manual tem em relação a nenhum cliente pode ter desconto de 50%.
  Confiança: -0.19577766109157252

Segmento 236:
  Início: 1948.0s
  Fim: 1960.0s
  Texto:  O que acontece no meio dessa história? Vamos imaginar que o vendedor, ele está falando com uma inteligência artificial para perguntar até quanto de desconto ele pode dar.
  Confiança: -0.10619687182562691

Segmento 237:
  Início: 1960.0s
  Fim: 1966.0s
  Texto:  E eventualmente ele pergunta se ele pode dar desconto de 50%.
  Confiança: -0.10619687182562691

Segmento 238:
  Início: 1966.0s
  Fim: 1978.0s
  Texto:  Agora, vamos imaginar que na hora que a gente fez esse splitter aqui, ele cortou em dois documentos. Ele cortou em dois documentos da seguinte forma.
  Confiança: -0.1692847322534632

Segmento 239:
  Início: 1978.0s
  Fim: 1987.0s
  Texto:  Ele pegou uma parte desse documento aqui, até aqui.
  Confiança: -0.1692847322534632

Segmento 240:
  Início: 1988.0s
  Fim: 1994.0s
  Texto:  Então ele pegou essa parte do documento no documento 1.
  Confiança: -0.15705157410014758

Segmento 241:
  Início: 1994.0s
  Fim: 2004.0s
  Texto:  E essa parte aqui, que é cliente pode ter de 50% de desconto, ficou no segundo documento.
  Confiança: -0.15705157410014758

Segmento 242:
  Início: 2005.0s
  Fim: 2016.0s
  Texto:  Legal? Aí eu fiz minha busca vetorial e a divinha qual documento que eu trouxe para mim inteligência artificial.
  Confiança: -0.1726687989144955

Segmento 243:
  Início: 2016.0s
  Fim: 2024.0s
  Texto:  Eu trouxe exatamente o pedaço que fala cliente pode ter de 50% de desconto.
  Confiança: -0.1726687989144955

Segmento 244:
  Início: 2024.0s
  Fim: 2031.0s
  Texto:  Então na hora que alguém for fazer uma pergunta, aí vai falar assim, cliente pode ter 50% de desconto.
  Confiança: -0.1285011179306928

Segmento 245:
  Início: 2031.0s
  Fim: 2036.0s
  Texto:  Porque o documento que foi cortado cortou a palavra nenhum.
  Confiança: -0.1285011179306928

Segmento 246:
  Início: 2038.0s
  Fim: 2044.0s
  Texto:  Vocês entendem o tipo de problema que a gente pode estar aí no meio dessa história?
  Confiança: -0.1285011179306928

Segmento 247:
  Início: 2044.0s
  Fim: 2049.0s
  Texto:  Faz sentido para você o nível de risco que você pode ter de trabalhar dessa forma?
  Confiança: -0.1285011179306928

Segmento 248:
  Início: 2050.0s
  Fim: 2058.0s
  Texto:  Então a grande sacada aqui nesse ponto é como que eu tento minimizar isso aí?
  Confiança: -0.10503148379391186

Segmento 249:
  Início: 2059.0s
  Fim: 2064.0s
  Texto:  Isso aí é um ponto importante que eu queria aqui trabalhar com vocês.
  Confiança: -0.10503148379391186

Segmento 250:
  Início: 2064.0s
  Fim: 2070.0s
  Texto:  Porque no final das contas, é isso que vai acontecer no dia a dia na empresa.
  Confiança: -0.10503148379391186

Segmento 251:
  Início: 2070.0s
  Fim: 2074.0s
  Texto:  E é isso que já está acontecendo em muitas empresas.
  Confiança: -0.10503148379391186

Segmento 252:
  Início: 2074.0s
  Fim: 2084.0s
  Texto:  Você passar informação errada para inteligência artificial, pelo fato de que as buscas que você fez no banco de dados trouxeram informações incompletas,
  Confiança: -0.24329015096028647

Segmento 253:
  Início: 2084.0s
  Fim: 2092.0s
  Texto:  ou trouxeram informações que fizeram com que ia entender se é exatamente o oposto daquilo que você queria treinar para ela.
  Confiança: -0.24329015096028647

Segmento 254:
  Início: 2092.0s
  Fim: 2095.0s
  Texto:  Fei sentido?
  Confiança: -0.24329015096028647

Segmento 255:
  Início: 2096.0s
  Fim: 2103.0s
  Texto:  Pessoal, todo mundo está conseguindo entender esse tipo de problema que eu estou trazendo aqui para vocês?
  Confiança: -0.20309214158491654

Segmento 256:
  Início: 2104.0s
  Fim: 2114.0s
  Texto:  É bem importante que vocês entendam. Eu acho que não é algo tão complexo de entender se você cortar um documento, você vai informar essa informação errada para ir.
  Confiança: -0.20309214158491654

Segmento 257:
  Início: 2115.0s
  Fim: 2128.0s
  Texto:  Agora, o lance aqui é o seguinte. Como que nós podemos trabalhar de uma forma que evite ou minimize um pouco esse problema?
  Confiança: -0.14495545002951551

Segmento 258:
  Início: 2128.0s
  Fim: 2134.0s
  Texto:  Uma das formas de você minimizar um pouco esses tipos de problema é o seguinte.
  Confiança: -0.14495545002951551

Segmento 259:
  Início: 2134.0s
  Fim: 2138.0s
  Texto:  É você trabalhar com um conceito que é chamado de Overlapping.
  Confiança: -0.14495545002951551

Segmento 260:
  Início: 2139.0s
  Fim: 2147.0s
  Texto:  O que o Overlapping faz no final das contas? Eu falo que eu vou ter documentos de mil caracteres, por exemplo,
  Confiança: -0.1423787320597788

Segmento 261:
  Início: 2147.0s
  Fim: 2155.0s
  Texto:  e com 100 caracteres de Overlapping. O que isso significa no final das contas?
  Confiança: -0.1423787320597788

Segmento 262:
  Início: 2155.0s
  Fim: 2159.0s
  Texto:  Que eu vou, sim, ter meus documentos de mil caracteres.
  Confiança: -0.1423787320597788

Segmento 263:
  Início: 2159.0s
  Fim: 2166.0s
  Texto:  Mas eu vou voltar 100 caracteres para trás e vou também trazer essa informação.
  Confiança: -0.1423787320597788

Segmento 264:
  Início: 2166.0s
  Fim: 2176.0s
  Texto:  Porque se eu trouxer vários documentos, uma atrás do outro, aí ela consegue perceber que um documento é uma contínuação do outro.
  Confiança: -0.16052872152889477

Segmento 265:
  Início: 2176.0s
  Fim: 2181.0s
  Texto:  Então, isso aí acaba minimizando um pouco esse problema.
  Confiança: -0.16052872152889477

Segmento 266:
  Início: 2181.0s
  Fim: 2190.0s
  Texto:  O problema que eu acabei de mostrar para vocês, ele entese, ele é resolvido, nesse caso simples, com a história do Overlapping.
  Confiança: -0.16052872152889477

Segmento 267:
  Início: 2190.0s
  Fim: 2195.0s
  Texto:  Porque ele iria pegar nenhum também e jogar no mesmo documento.
  Confiança: -0.16634148709914265

Segmento 268:
  Início: 2195.0s
  Fim: 2201.0s
  Texto:  Então, isso aí é um ponto, assim, importante para vocês entenderem.
  Confiança: -0.16634148709914265

Segmento 269:
  Início: 2201.0s
  Fim: 2211.0s
  Texto:  Então, basicamente eu tenho mil caracteres, eu vou pegar 100 caracteres anteriores a esse mil e vou jogar aqui para mim também.
  Confiança: -0.16634148709914265

Segmento 270:
  Início: 2212.0s
  Fim: 2217.0s
  Texto:  Beleza? E isso vai acontecer com 100% dos meus documentos.
  Confiança: -0.09662989290749155

Segmento 271:
  Início: 2217.0s
  Fim: 2226.0s
  Texto:  Então, todos os meus documentos, eles vão ter duplicação em uma quantidade de caracteres que você vai colocar ali para ele.
  Confiança: -0.09662989290749155

Segmento 272:
  Início: 2226.0s
  Fim: 2235.0s
  Texto:  Beleza? Então, isso aí é uma das coisas, assim, que são importantes de a gente conseguir trazer e entender ali para a gente.
  Confiança: -0.09662989290749155

Segmento 273:
  Início: 2236.0s
  Fim: 2239.0s
  Texto:  Overlapping, normalmente, ele vai pegar tudo que está atrás.
  Confiança: -0.12624158009444134

Segmento 274:
  Início: 2239.0s
  Fim: 2247.0s
  Texto:  Agora, uma coisa que vocês vão perceber também é que existem algoritmos para você fazer um split que eles são um pouco mais inteligentes.
  Confiança: -0.12624158009444134

Segmento 275:
  Início: 2247.0s
  Fim: 2261.0s
  Texto:  A invés de ele cortar na carne, vamos dizer assim, ou a quantidade de caracteres, ele tenta identificar o que é um parágrafo, o que é uma frase, o que é uma sentença, onde tem ponto.
  Confiança: -0.12624158009444134

Segmento 276:
  Início: 2261.0s
  Fim: 2268.0s
  Texto:  Então, como isso, ele evita encortar, por exemplo, um parágrafo totalmente no meio.
  Confiança: -0.13820490965972076

Segmento 277:
  Início: 2268.0s
  Fim: 2285.0s
  Texto:  Então, quando eu estou falando de cortar documento e etc., na prática também não é tão dessa forma, porque esses algoritmos que fazem esse split, eles têm diversas opções para conseguir minimizar.
  Confiança: -0.13820490965972076

Segmento 278:
  Início: 2285.0s
  Fim: 2290.0s
  Texto:  Então, ele corta um documento até pular de linha, ele começa o outro quando pulou.
  Confiança: -0.1522393544514974

Segmento 279:
  Início: 2290.0s
  Fim: 2296.0s
  Texto:  Então, não necessariamente todos os documentos, nesse caso, teriam exatamente mil caracteres.
  Confiança: -0.1522393544514974

Segmento 280:
  Início: 2296.0s
  Fim: 2305.0s
  Texto:  Fez sentido isso para vocês. Então, isso ajuda a minimizar. E o Overlapping também ajuda a minimizar.
  Confiança: -0.1522393544514974

Segmento 281:
  Início: 2305.0s
  Fim: 2317.0s
  Texto:  Uma outra coisa que vai ajudar a minimizar é que você também, quando você faz essa busca no banco de dados, você tem duas coisas importantes ali para você.
  Confiança: -0.1270944444756759

Segmento 282:
  Início: 2317.0s
  Fim: 2324.0s
  Texto:  Você vai ter o K, que é a quantidade de documentos que você quer.
  Confiança: -0.1270944444756759

Segmento 283:
  Início: 2324.0s
  Fim: 2330.0s
  Texto:  Quanto mais documento você quer, mais chance você tem de trazer mais de informação.
  Confiança: -0.1270944444756759

Segmento 284:
  Início: 2330.0s
  Fim: 2335.0s
  Texto:  Quanto mais informação mais contexto, mais chance de ar responder certo.
  Confiança: -0.17880275761969736

Segmento 285:
  Início: 2335.0s
  Fim: 2347.0s
  Texto:  Quanto mais documento você colocar, mais tokens você gasta, mais caro a solução fica. Então, você tem que encontrar também um número que você vai testar idealmente.
  Confiança: -0.17880275761969736

Segmento 286:
  Início: 2347.0s
  Fim: 2359.0s
  Texto:  Uma outra informação que você vai ter é o score. Porque é o score? Porque, quando a gente está trabalhando com IAA, a gente não está trabalhando com algo determinístico, que é 1 ou 0.
  Confiança: -0.17880275761969736

Segmento 287:
  Início: 2359.0s
  Fim: 2373.0s
  Texto:  Nós estamos trabalhando com probabilidades. Então, o que isso significa? Muitas vezes, aí a minha busca no banco de dados vai trazer, por exemplo, um documento com um score 0.3.
  Confiança: -0.11617075403531392

Segmento 288:
  Início: 2373.0s
  Fim: 2385.0s
  Texto:  Eu falo, cara, um documento com um score de 0.3, provavelmente ele está bem fora para responder de fato o que IAA está precisando trazer.
  Confiança: -0.15243624732607886

Segmento 289:
  Início: 2385.0s
  Fim: 2392.0s
  Texto:  Então, eu não vou usar um documento com score 0.3. Posso colocar uma regra. Eu só vou responder de 0.7 para cima.
  Confiança: -0.15243624732607886

Segmento 290:
  Início: 2392.0s
  Fim: 2397.0s
  Texto:  E se eu não tiver pelo menos tantos documentos, eu vou falar que eu não sei para IAA falar que ela não sabe.
  Confiança: -0.15243624732607886

Segmento 291:
  Início: 2398.0s
  Fim: 2422.0s
  Texto:  Fechou? Então, assim, pessoal, não é pegar qualquer coisa e retornar ali para IAA. Você consegue gerencer a quantidade de documentos, você consegue gerencer o score do resultado para que você tenha mais segurança do que você vai disponibilizar ali para IAA, ali para você conseguir trabalhar. Legal?
  Confiança: -0.12465987334380278

Segmento 292:
  Início: 2423.0s
  Fim: 2438.0s
  Texto:  O Paulo fez uma pergunta interessante. Como que eu vou recuperar isso no banco de dados se você tem que calcular o vetor da busca? E é pelo sei, pelo vetor, como é que é feito isso? Cara, vocês vão ver, eu vou mostrar isso na prática para vocês.
  Confiança: -0.13347708095203747

Segmento 293:
  Início: 2438.0s
  Fim: 2458.0s
  Texto:  Daqui a pouquinho, eu vou mostrar código e vou mostrar isso na prática acontecendo. Esses tipos de banco de dados vetoriais são prontos para isso. O que isso significa que você dá uma busca, passando um vetor, e ele vai retornar os registros que têm os vetores com as maiores probabilidades para você.
  Confiança: -0.15648325895651793

Segmento 294:
  Início: 2459.0s
  Fim: 2466.0s
  Texto:  Então, é basicamente isso que acontece. Não tem muita novidade em relação aí para a gente.
  Confiança: -0.24593013763427735

Segmento 295:
  Início: 2466.0s
  Fim: 2476.0s
  Texto:  A baseado nisso, pessoal, o que vai acontecer aqui no nosso caso? Eu votei a junção de documentos e vou trabalhar dessa forma.
  Confiança: -0.19889876047770183

Segmento 296:
  Início: 2476.0s
  Fim: 2493.0s
  Texto:  Vou dizer uma coisa, dependendo da situação ou do contexto que você está, isso vai resolver o seu problema. De forma geral, você vai ter um grande incidia certo na hora de trabalhar. Então, isso aí é um ponto importante para vocês.
  Confiança: -0.19889876047770183

Segmento 297:
  Início: 2493.0s
  Fim: 2510.0s
  Texto:  Agora, o ponto é que a vida ela não é simples. Por quê? Porque muitas vezes você não tem apenas um PDF. Você tem um monte de documento.
  Confiança: -0.1469687913593493

Segmento 298:
  Início: 2510.0s
  Fim: 2528.0s
  Texto:  E esses montes de documento, eles estão em domínios diferentes, em categorias diferentes. Eles têm informações diferentes, etc. para vocês. Então, eu vou dar um exemplo aqui para que vocês consigam entender um pouquinho na prática, porque eu sabia que essas dúvidas iriam surgir.
  Confiança: -0.171767417122336

Segmento 299:
  Início: 2529.0s
  Fim: 2538.0s
  Texto:  Eu tenho uma pasta aqui que eu criei no meu Google Docs, que ele chama MBA IA.
  Confiança: -0.2848982981273106

Segmento 300:
  Início: 2539.0s
  Fim: 2550.0s
  Texto:  Beleza? Essa pasta MBA IA no meu Google Drive, ele tem diversos documentos. Quais documentos ele tem aqui? Ele tem um MBA IA Overview.
  Confiança: -0.1025742318895128

Segmento 301:
  Início: 2550.0s
  Fim: 2564.0s
  Texto:  Se eu entrar nesse documento aqui, eu coloquei para ficar bem simples de vocês entenderem. Logo no cabeçalho, algumas informações interessantes. Por exemplo, curso ID MBA IA.
  Confiança: -0.1025742318895128

Segmento 302:
  Início: 2565.0s
  Fim: 2573.0s
  Texto:  Tipo do documento, é um overview. É uma pós-graduação. Ele tem certificado no Mac e ele tem uma duração de 12 meses.
  Confiança: -0.17785959040864985

Segmento 303:
  Início: 2573.0s
  Fim: 2588.0s
  Texto:  E aqui, para mim, tem nome do curso, promessa do curso, contextualização, pilares, formato das aulas, break out rooms, talkies com especialistas, sessões de mentoria, laboratórios práticos,
  Confiança: -0.17785959040864985

Segmento 304:
  Início: 2589.0s
  Fim: 2596.0s
  Texto:  as disciplinas que esse MBA tem, com quem que vou aprender, como é que eu certificado e algumas perguntas frequentes.
  Confiança: -0.16605420015296157

Segmento 305:
  Início: 2596.0s
  Fim: 2608.0s
  Texto:  Então vamos imaginar que eu estou querendo fazer o MBA que possa tirar dúvidas sobre o meu MBA. Beleza? Agora, a questão aqui é o seguinte.
  Confiança: -0.16605420015296157

Segmento 306:
  Início: 2608.0s
  Fim: 2614.0s
  Texto:  Imagina que você chegue e faz uma pergunta o seguinte. Quais são as disciplinas que tem no MBA?
  Confiança: -0.16605420015296157

Segmento 307:
  Início: 2614.0s
  Fim: 2626.0s
  Texto:  Eu vou fazer uma busca vetorial e eu vou fazer o seguinte. Bom, ele está falando no MBA. Então eu vou buscar todo o documento onde o ID tem a MBA no curso.
  Confiança: -0.21511402377834568

Segmento 308:
  Início: 2626.0s
  Fim: 2633.0s
  Texto:  Porque daí eu já elimino qualquer outro tipo de curso que não seja o MBA. Aí eu já funilo a minha busca vetorial.
  Confiança: -0.21511402377834568

Segmento 309:
  Início: 2634.0s
  Fim: 2647.0s
  Texto:  Outra coisa, ele perguntou quais as disciplinas. Então eu vou buscar e provavelmente em um dos documentos, a por conta da busca que o cara perguntou, vai ter um trecho chamado de disciplinas.
  Confiança: -0.2064620148051869

Segmento 310:
  Início: 2647.0s
  Fim: 2655.0s
  Texto:  E aí, baseado nisso, vai ser respondido para a IA. Vamos imaginar que lá no IA no prompt eu coloco as disciplinas do MBA.
  Confiança: -0.2064620148051869

Segmento 311:
  Início: 2655.0s
  Fim: 2668.0s
  Texto:  Mas daí o que acontece? O que é o seguinte? Aí a pessoa pergunta, está, mas o que eu vou ver em fundamentos de arquitetura de software com IA.
  Confiança: -0.19267690181732178

Segmento 312:
  Início: 2668.0s
  Fim: 2681.0s
  Texto:  Nesse documento tem aqui o que tem no conteúdo programático de arquitetura de software com IA. Não tem essa informação.
  Confiança: -0.19267690181732178

Segmento 313:
  Início: 2682.0s
  Fim: 2700.0s
  Texto:  Então o que acontece? Eu posso ter outro tipo de documento, por exemplo, de disciplinas. E daí eu vou pegar qualquer disciplina aqui ao fundamento de arquitetura.
  Confiança: -0.1259950456165132

Segmento 314:
  Início: 2701.0s
  Fim: 2712.0s
  Texto:  Nesse documento de fundamento de arquitetura eu tenho que o curso ID é o MBA. Eu sei que ele é um tipo de disciplina. Eu sei que o ID da disciplina é fundamentos de arquitetura de software com IA.
  Confiança: -0.13925159370506202

Segmento 315:
  Início: 2712.0s
  Fim: 2719.0s
  Texto:  E o Pilar, que faz parte, é o Pilar de arquitetura de software. E aqui tem todos os detalhes da disciplina.
  Confiança: -0.13925159370506202

Segmento 316:
  Início: 2720.0s
  Fim: 2740.0s
  Texto:  Então, quando eu começo a trabalhar dessa forma, se você perceber, eu começo a ter uma estrutura um pouco mais inteligente. Porque se o cara faz uma pergunta geral, eu posso fazer um pré-processamento da praia antes para ela entender a intenção do usuário.
  Confiança: -0.1439459708429152

Segmento 317:
  Início: 2740.0s
  Fim: 2760.0s
  Texto:  Como assim? Vamos imaginar que eu pergunte o seguinte. Me falho mais sobre o MBA em engenharia de software com IA. Essa é a pergunta. Então o que eu vou fazer, primeiramente, galera?
  Confiança: -0.18829324510362414

Segmento 318:
  Início: 2760.0s
  Fim: 2771.0s
  Texto:  Eu posso aqui usar uma estratégia. Eu posso, antes de sair buscando no banco de dados, fazer um pronto pra me IA fazendo o seguinte.
  Confiança: -0.18918032481752592

Segmento 319:
  Início: 2772.0s
  Fim: 2784.0s
  Texto:  Tente entender a intenção do usuário com essa pergunta. E baseado na intenção,
  Confiança: -0.18918032481752592

Segmento 320:
  Início: 2785.0s
  Fim: 2810.0s
  Texto:  define para mim quais são as chances de isso ser uma pergunta geral, ou seja, que é o meu documento de overview, ou uma pergunta específica de alguma coisa.
  Confiança: -0.15328152974446616

Segmento 321:
  Início: 2811.0s
  Fim: 2822.0s
  Texto:  Então, o que vai acontecer? E eu posso falar ainda assim, responda em um JSON do seguinte tipo.
  Confiança: -0.1860926946004232

Segmento 322:
  Início: 2823.0s
  Fim: 2846.0s
  Texto:  Eu posso pedir para ele responder onde o curso ID, MBA IA, porque ele fez uma pergunta do MBA em engenharia de software com IA, ele vai responder pra mim que o tipo de documento que ele quer é um documento de overview, porque ele fez uma pergunta generalista aqui pra mim.
  Confiança: -0.14413931672002228

Segmento 323:
  Início: 2847.0s
  Fim: 2859.0s
  Texto:  Ok? Uma vez que eu tenho esse JSON aqui na hora de eu fazer a busca no meu banco de dados vetorial, o que eu vou fazer? Eu vou fazer o seguinte.
  Confiança: -0.15280402091241652

Segmento 324:
  Início: 2860.0s
  Fim: 2874.0s
  Texto:  Busque usando a pergunta vetorizada do usuário no banco, onde os metadados,
  Confiança: -0.15280402091241652

Segmento 325:
  Início: 2877.0s
  Fim: 2888.0s
  Texto:  digam que o curso ID, MBA IA, e o tipo é o overview.
  Confiança: -0.18662100252897842

Segmento 326:
  Início: 2889.0s
  Fim: 2898.0s
  Texto:  Aí, o que vai acontecer, galera? Vocês conseguem concordar comigo que os registros que eles vão trazer aqui no meu banco de dados vai vir muito menos tranqueira?
  Confiança: -0.16820136706034342

Segmento 327:
  Início: 2898.0s
  Fim: 2906.0s
  Texto:  A chance de ele trazer dados da informação da nossa pós-graduação com Go é muito menor. Vocês concordam comigo?
  Confiança: -0.16820136706034342

Segmento 328:
  Início: 2907.0s
  Fim: 2916.0s
  Texto:  Por que? Porque eu fiz um pré-processamento da pergunta. Eu tentei entender a intenção do usuário na pergunta.
  Confiança: -0.16959307743952826

Segmento 329:
  Início: 2916.0s
  Fim: 2927.0s
  Texto:  E baseado nessa intenção, eu consigo fazer filtros no meu banco de dados pra eu entender melhor quais tipos de documento que eu vou trabalhar.
  Confiança: -0.16959307743952826

Segmento 330:
  Início: 2928.0s
  Fim: 2937.0s
  Texto:  Percebe? Aí a coisa fica um pouco mais fácil pra eu conseguir trabalhar. Na hora que eu for perguntar, eu tenho esses metadados.
  Confiança: -0.11198241370064872

Segmento 331:
  Início: 2937.0s
  Fim: 2948.0s
  Texto:  Então, quando a gente está falando em estruturar os dados no banco de dados, eu tenho o conteúdo puro que vai ser a informação pra IA,
  Confiança: -0.11198241370064872

Segmento 332:
  Início: 2949.0s
  Fim: 2960.0s
  Texto:  eu tenho o vetor desse conteúdo e eu tenho metadados que vão me ajudar a eu fazer os meus filtros das melhores formas pra eu conseguir filtrar essa parada toda.
  Confiança: -0.1639489303400487

Segmento 333:
  Início: 2962.0s
  Fim: 2972.0s
  Texto:  Então, isso aí é um ponto importante. Beleza? Tem gente pergunta, mas aí a não pode gerar uma interpretação errada da interpretação da parte dela,
  Confiança: -0.1639489303400487

Segmento 334:
  Início: 2972.0s
  Fim: 2985.0s
  Texto:  e por isso que nós temos mecanismos de evaluation e testes onde você passa um set de diversas perguntas nos mais diversos tipos de formato e você avalia se ela está respondendo certo ou não.
  Confiança: -0.150977958753271

Segmento 335:
  Início: 2985.0s
  Fim: 2999.0s
  Texto:  Caso ela não esteja respondendo certo, você vai melhorar o pront. E você vai melhorar o pront falando, se ele fizer perguntas mais gerais, se ele fizer algo que não sei o quê, use dessa forma.
  Confiança: -0.150977958753271

Segmento 336:
  Início: 2999.0s
  Fim: 3017.0s
  Texto:  Mas se ele falar disciplina, já usa o tipo disciplina. Se ele falar professor, já usa o documento do tipo de professores, entendeu? Então, o pront, você consegue pedir pra IA, entendendo os seus tipos de documento, ela ser muito mais precisa pra trabalhar dessa forma.
  Confiança: -0.17709093935349407

Segmento 337:
  Início: 3017.0s
  Fim: 3031.0s
  Texto:  Então, a grande sacada aí é essa. Tem gente que perguntou, aí a funila ou embedin, não é que ela funila embedin. O embedin da pergunta vai ser o mesmo. O que aí a vai afunilar?
  Confiança: -0.2380909515639483

Segmento 338:
  Início: 3031.0s
  Fim: 3045.0s
  Texto:  Estamos filtros que ela vai fazendo o banco de dados por conta de metadados que você vai extrair baseado na pergunta que a pessoa fez. Então, isso aí é um ponto importante aí pra o que vocês conseguam trabalhar.
  Confiança: -0.18529737912691557

Segmento 339:
  Início: 3045.0s
  Fim: 3069.0s
  Texto:  Depois disso, pessoal, existem outras técnicas, porque você vai receber vários documentos de diversos tipos. Você pode fazer um processo que é chamado de re-rank que você pode pedir pra IA, pegar todos aqueles resultados e re-ranquear por relevância baseado na pergunta, tem muita coisa avançada pra trabalhar com Ragn, tá? Mas isso só queria trazer essa ideia básica aqui pra vocês.
  Confiança: -0.19614615807166466

Segmento 340:
  Início: 3069.0s
  Fim: 3084.0s
  Texto:  Fê sentido isso aqui pra vocês, galera, o que eu acabei de falar de forma geral, eu não quero que ninguém vir um expert em Ragn aqui. Eu só queria que você entendesse a ideia de como que isso é possível você trabalhar.
  Confiança: -0.20631630080086844

Segmento 341:
  Início: 3085.0s
  Fim: 3103.0s
  Texto:  E, obviamente, dá pra perceber que não é trivial, porque você vai ter que pegar todos os documentos e eu cá num banco de dados. Aí você tem que ser paramento a dado, você tem que ter um monte de técnicas pra você conseguir fazer isso. Não é fácil, principalmente quando você tem muita informação. Mas o conceito básico é esse.
  Confiança: -0.1782893168775341

Segmento 342:
  Início: 3103.0s
  Fim: 3118.0s
  Texto:  Pegar a pergunta, consultar no banco o que tem mais de relevante e injetar isso no pronto. Beleza? Como que isso pode ser feito, pessoal, vou mostrar aqui pra vocês verem alguns exemplos, tá?
  Confiança: -0.19977406757633862

Segmento 343:
  Início: 3118.0s
  Fim: 3129.0s
  Texto:  Deixa eu pegar aqui um exemplo simples, e depois eu vou mostrar um pouquinho, menos simples aqui pra vocês, tá?
  Confiança: -0.19977406757633862

Segmento 344:
  Início: 3129.0s
  Fim: 3153.0s
  Texto:  Somente pra vocês saberem, a gente no nosso MBA, nós criamos algumas aulas que a gente tá começando a disponibilizar de inivelamento, porque a gente sentiu que algumas turmas nossas, tinha gente que não conhecia nada de MCP, tinha gente que era mais avançada, tinha gente que queria num módulo mais teórico como o de fundamentos,
  Confiança: -0.17398518529431573

Segmento 345:
  Início: 3153.0s
  Fim: 3180.0s
  Texto:  a gente já vai colocar um pouco mais a mão na massa, então a gente recebeu esse feedback e pra atender os alunos a gente tá criando vários cursos em paralelos pra que elas possam fazer. Um desses cursos é um curso bem tranquilo de Lang Chen, que é um freme work, que te ajuda a trabalhar de uma forma muito mais simples com chamadas aí com inteligência artificial.
  Confiança: -0.2336015188565818

Segmento 346:
  Início: 3180.0s
  Fim: 3194.0s
  Texto:  É algo que é mais simples, eu digo bem simples do tipo isso aqui, tá? Como que eu faço um Hello World pra mim, a... Eu passo Hello World e aí, a me responde, com três linhas eu fiz uma chamada no modelo de A.
  Confiança: -0.32160847981770835

Segmento 347:
  Início: 3194.0s
  Fim: 3206.0s
  Texto:  Vocês entenderem? Vocês entenderem o que eu tô querendo dizer, como a Lang Chen ela consegue abstrair muito pra vocês, eu não vou dar aula de Lang Chen aqui pra vocês, tô querendo dar esse exemplo aqui pra vocês, tá?
  Confiança: -0.21221782654289187

Segmento 348:
  Início: 3206.0s
  Fim: 3221.8s
  Texto:  Então, somente pra vocês entenderem essa ideia aqui, tá? Só pra vocês saberem, existem um trilhão de freme work de ferramentos, tem Lang Chen, Lang Graph que vem em cima do Lang Chen, aí tem observabilidade com Lang Smith, aí você tem IDK, você tem Crueia,
  Confiança: -0.21221782654289187

Segmento 349:
  Início: 3221.8s
  Fim: 3244.8s
  Texto:  aí caras, você tem um monte de ferramenta de A pra fazer isso, tá? Agora, não é que Lang Chen já era, tá? Lang Chen, tá? Ela é base inclusive pra freme work como o Lang Graph, tá? Então é importante você saber exatamente isso, porque você não precisa, às vezes, de um freme work pra fazer algo muito simples, tá?
  Confiança: -0.15697913450353285

Segmento 350:
  Início: 3244.8s
  Fim: 3264.8s
  Texto:  Então, por isso que é importante você entender esses fundamentos e tudo mais, tá? Agora, o que que acontece? O lance é o seguinte, tem uma parada aqui que eu mostro e eu vou trazer esse exemplo pra que vocês vejam que não é difícil, tá?
  Confiança: -0.1291808005302183

Segmento 351:
  Início: 3264.8s
  Fim: 3283.8s
  Texto:  Mas, apesar de ter bastante código e tudo mais, mas ele não é um exemplo difícil, tá? Basicamente nesse exemplo, o que que eu tô fazendo, a grosso modo, eu estou lendo um documento PDF, chamado GPT-5 aqui pra mim como ponto PDF, beleza?
  Confiança: -0.26357832951332205

Segmento 352:
  Início: 3283.8s
  Fim: 3312.8s
  Texto:  Tudo mundo comigo aqui? Maravilha. Segundo passo que eu vou fazer aqui, eu vou fazer o meu splitter, ou seja, eu vou pegar o conteúdo desse meu PDF e usar um cara chamado Recursive Character Texts Displitter, onde eu vou falar pra ele separar em chunks de mil caracteres aqui, com o Overlap de 550.
  Confiança: -0.23798310002194176

Segmento 353:
  Início: 3313.8s
  Fim: 3334.8s
  Texto:  E quando ele fazer esse splitter, ele vai explitar esse documento aqui pra mim. Maravilha, tranquilo aí pra vocês, ou seja, com a regém do documento na memória e fiz o splitter desse documento, e eu vou receber uma lista desses documentos aqui pra mim.
  Confiança: -0.1839783612419577

Segmento 354:
  Início: 3335.8s
  Fim: 3355.8s
  Texto:  Uma vez que eu fiz isso, eu estou criando aqui pra mim, tá, uma lista de documentos que vai pegar o quê? Vai ter um peixe-contente que é o conteúdo do documento que foi explitado, tá? Ou seja, o conteúdo puro mesmo desse documento.
  Confiança: -0.16265030761263263

Segmento 355:
  Início: 3355.8s
  Fim: 3368.8s
  Texto:  E eu criei aqui também, tá? Uma chave de meta data, onde eu vou pegar todos os metadados do PDF, tá? E vou adicionar aqui num dicionário aqui pra mim.
  Confiança: -0.1577297424783512

Segmento 356:
  Início: 3368.8s
  Fim: 3395.8s
  Texto:  Se esse metadado do PDF, que vem em branco, ele nem vai trazer pra mim. Por que eu estou dizendo isso? Quando eu faço o Texts Displitter aqui pra mim, no PDF, ele vai trazer pra mim os metadados do PDF, ou seja, quem gerou PDF, a data do PDF, o autor do PDF, o programa que gerou o PDF, o autor, sabe essas paradas dos metadados? Então é basicamente isso que acaba acontecendo.
  Confiança: -0.1465664177297432

Segmento 357:
  Início: 3395.8s
  Fim: 3407.8s
  Texto:  Então o que eu estou fazendo aqui é criando uma lista de documentos aqui pra mim, onde eu tenho o conteúdo cru do que foi explitado e os metadados que vieram pra mim nesse meu PDF.
  Confiança: -0.1395576997236772

Segmento 358:
  Início: 3408.8s
  Fim: 3409.8s
  Texto:  Beleza?
  Confiança: -0.1395576997236772

Segmento 359:
  Início: 3409.8s
  Fim: 3420.8s
  Texto:  Tudo mundo entendeu? Até agora, galera. Baixo que eu não quero que vocês entendam detalhes de panto, e como é que sei lá, você faz um fora aqui dessa forma, tá?
  Confiança: -0.18767344800731803

Segmento 360:
  Início: 3420.8s
  Fim: 3428.8s
  Texto:  Mas o importante é que vocês entendam que eu tenho uma lista com todos os documentos, com conteúdos documentos e com a parada aqui.
  Confiança: -0.18767344800731803

Segmento 361:
  Início: 3428.8s
  Fim: 3435.8s
  Texto:  Outra coisa que eu fiz aqui, tá? Eu resolvi criar meus IDS personalizados pra cada documento desse.
  Confiança: -0.1503156534830729

Segmento 362:
  Início: 3435.8s
  Fim: 3447.8s
  Texto:  Eu normalmente gosto de criar os índices personalizados dos IDS, porque no nome do índice eu posso colocar documento Overview 1, página 1, documento Overview, página 2.
  Confiança: -0.1503156534830729

Segmento 363:
  Início: 3447.8s
  Fim: 3461.8s
  Texto:  Eu posso colocar documento, profe disciplinas 1, documento disciplina 1, documento, fica mais fácil pra você buscar inclusive nos índices e ter mais facilidades ali pra gente, tá?
  Confiança: -0.1918960104183275

Segmento 364:
  Início: 3461.8s
  Fim: 3476.8s
  Texto:  Então isso aí é um ponto importante pra vocês conseguirem trabalhar, tá? E aí o que eu estou fazendo aqui no meio da história é o seguinte, eu estou falando que eu vou usar um modelo de embedin da opinião.
  Confiança: -0.1918960104183275

Segmento 365:
  Início: 3476.8s
  Fim: 3494.8s
  Texto:  Esse modelo chama text embed in 3-small, que tem uma quantidade de dimensões no vetor, tem o larga que tem mais quantidade de dimensões e existem outros modelos de embedding que não são da opinião, que dá a opinião que você pode utilizar inclusive, tá?
  Confiança: -0.23408304989992917

Segmento 366:
  Início: 3494.8s
  Fim: 3502.8s
  Texto:  E o que acontece agora? Existem diversos tipos de bancos de dados vetoriais, tá?
  Confiança: -0.23408304989992917

Segmento 367:
  Início: 3502.8s
  Fim: 3509.8s
  Texto:  Existem bancos de dados vetoriais que nasceram já com esse propósito, vou dar um exemplo pra você que é o paincon, tá?
  Confiança: -0.16372547830854142

Segmento 368:
  Início: 3509.8s
  Fim: 3529.8s
  Texto:  O paincon ele faz exatamente isso, ele é um banco, ele roda como serviço, você não tem que ficar se preocupando com escala, ele consegue trabalhar de forma cheia diada, de uma forma muito fantástica, ele tem alta performance e mesmo quando você tem milhões e mais milhões de vetores indexados, tá?
  Confiança: -0.16372547830854142

Segmento 369:
  Início: 3530.8s
  Fim: 3546.8s
  Texto:  Agora, se tem um banco de dados que é super útil nos dias de hoje, é o Possegress, tá? O Possegress ele tem extensões, uma dessas extensões é chamado de PGVector, que é uma forma de você conseguir fazer com que o Possegress vira um banco de dados vetorial pra você fazer esse tipo de busca.
  Confiança: -0.18033194541931152

Segmento 370:
  Início: 3546.8s
  Fim: 3558.8s
  Texto:  O PGEVector tem melhorado muito, tá? Mas existem ainda muitos benchmarks que mostram que alguns bancos de dados têm mais performance que o PGEVector, tá?
  Confiança: -0.10831008911132813

Segmento 371:
  Início: 3558.8s
  Fim: 3572.8s
  Texto:  Quando atinge casas de milhões e milhões de vetores? O que eu tô querendo dizer pra você é que muitos casos, se você já trabalha com Possegress, você ativa essa extensão e usa o PGEVector mesmo, entendeu?
  Confiança: -0.10831008911132813

Segmento 372:
  Início: 3572.8s
  Fim: 3589.8s
  Texto:  Agora, a gente tem que entender que se esses bancos de dados crescem muito, sempre tem trade-off, por exemplo, o Pinecon, ele é essa service e você não se preocupa com um monte de coisa, o Possegress pra você conseguir escalar ele, não é fácil, você vai ter que manjar de gerenciar a banco de dados,
  Confiança: -0.16847474234444754

Segmento 373:
  Início: 3589.8s
  Fim: 3600.8s
  Texto:  se você for trabalhar com um chat, você vai ter que trabalhar com sites. Então tem diversas questões aí que são estratégicas da empresa na adoção do tipo de banco de dados que você quer, tá?
  Confiança: -0.21864148548671178

Segmento 374:
  Início: 3600.8s
  Fim: 3615.8s
  Texto:  Nesse caso aqui, eu tô trabalhando com o PGEVector. Então, o que que acontece? Eu crie um estoura aqui chamado PGEVector, que é uma biblioteca, falei que o modelo de embeddement que eu tô utilizando é esse cara aqui,
  Confiança: -0.21864148548671178

Segmento 375:
  Início: 3615.8s
  Fim: 3623.8s
  Texto:  tô falando que o nome da minha collection, o que que é uma collection nome? É, vamos dizer que é uma coleção de documentos que eu vou criar, tá?
  Confiança: -0.14235382545285108

Segmento 376:
  Início: 3623.8s
  Fim: 3634.8s
  Texto:  Ou seja, como se fosse uma tag que eu posso separar com o nome que eu quiser pra eu chamar da forma que eu quiser, tá? Então é basicamente isso aí pra gente.
  Confiança: -0.14235382545285108

Segmento 377:
  Início: 3634.8s
  Fim: 3650.8s
  Texto:  O outra coisa é a conexão com o meu banco de dados, e eu falar pra ele também, pra ele trabalhar com JSON, Binary JSON na hora de ele fazer armazenamento e trabalhar com os dados, inclusive de metadados e coisas desse tipo.
  Confiança: -0.2046712778382382

Segmento 378:
  Início: 3650.8s
  Fim: 3675.8s
  Texto:  Então, o que eu vou fazer depois disso? Eu vou dar um estour, ed documentos, passo a minha lista de documentos que eu fiz o Explit, passei a minha lista de IDS que eu vou ter pra cada um desses documentos, e bomba, executo, e aquele meu GPT-5.PDF que é um documento grande, ele vai ser jogado no meu banco de dados.
  Confiança: -0.19648843170494162

Segmento 379:
  Início: 3675.8s
  Fim: 3701.8s
  Texto:  Ou é, Zleu, não acredito em você, ok? Eu vou aqui no meu banco de dados, vou aqui em tabelas, e você vai ver que tem uma tabela aqui chamada, aqui uma tabela minha aqui chamada, aqui tem um ID, um name que é o GPT-5 collection, que é o nome da collection que eu coloquei, e eu não coloquei nenhum metadado referente à collection, tá?
  Confiança: -0.2383883825623163

Segmento 380:
  Início: 3701.8s
  Fim: 3722.8s
  Texto:  E aqui eu tenho o meu banco de dados com os meus vetores, então o que isso significa, galera? Eu tenho o meu ID, eu tenho o ID da collection, eu tenho o embed que aqui é aquele vetor gigante que é onde a busca é realizada.
  Confiança: -0.13196091573746477

Segmento 381:
  Início: 3722.8s
  Fim: 3750.8s
  Texto:  Eu tenho aqui dado do documento, de qual foi o documento, e eu tenho aqui os meus metadados, e o meu metadado aqui é pagina zero, o caminho do documento que foi carregado, o criador, o mod date, trap, producer, esse cara, total page foi x59, creation date, ou seja, um monte de metadados que veio do PDF aqui pra mim, tá?
  Confiança: -0.18850268313759252

Segmento 382:
  Início: 3751.8s
  Fim: 3758.8s
  Texto:  Deu pra você sacarem o que aconteceu com esse tipo de formato, galera? Fez sentido isso aqui pra vocês?
  Confiança: -0.20623411851770737

Segmento 383:
  Início: 3759.8s
  Fim: 3771.8s
  Texto:  Beleza, então o que eu fiz foi a ingestão de um PDF fazendo aquele split em chanks usando as informações ali pra mim, tá? Beleza?
  Confiança: -0.20623411851770737

Segmento 384:
  Início: 3771.8s
  Fim: 3786.8s
  Texto:  Beleza? Mas simples do que parece, né? Mas é porque a gente tá com um script, né? Como eu disse pra vocês, coisas complexas não dá pra resolver de forma muito simples não. Mas pra esse exemplo dá bom aqui pra que vocês consegui trabalhar, beleza?
  Confiança: -0.1575804664975121

Segmento 385:
  Início: 3786.8s
  Fim: 3796.8s
  Texto:  Rola GeetHub, coisa desse tipo, galera, esse é material pra usar lunes do MBN, em gêneria, tá? Recomenos que você vira lunes, você vai ter muito mais material do que isso, tá bom?
  Confiança: -0.33554177535207647

Segmento 386:
  Início: 3797.8s
  Fim: 3810.8s
  Texto:  Outra coisa aqui, tá, é o seguinte. E eu tenho aqui um exemplo de busca vetorial, tá? Então aqui, basicamente, é a mesma coisa.
  Confiança: -0.33554177535207647

Segmento 387:
  Início: 3810.8s
  Fim: 3832.8s
  Texto:  O que eu vou fazer aqui é que agora eu tenho uma query que vamos dizer que a pergunta do usuário, tá? Então o que que acontece o seguinte? A pergunta é, tell me more about the GPT5 we think in evaluation and performance results comparing to the GPT4.
  Confiança: -0.22823648604135666

Segmento 388:
  Início: 3832.8s
  Fim: 3848.8s
  Texto:  Então essa é a pergunta que vamos dizer o usuário final tá fazendo. Então o que que eu vou fazer aqui? Eu vou pegar, tenho um modelo de embed, tá? Eu tenho o meu store que é o PGVector que eu coloquei. E agora o que que eu vou fazer?
  Confiança: -0.16399169690681226

Segmento 389:
  Início: 3848.8s
  Fim: 3860.8s
  Texto:  Eu vou pegar o meu store, fazer a minha busca por similaridade com score e vou trazer os três primeiros documentos. E perceba que eu estou colocando a query aqui.
  Confiança: -0.16264240916182354

Segmento 390:
  Início: 3860.8s
  Fim: 3872.8s
  Texto:  Galera, isso aqui tá bestraído num nível muito alto, porque por baixo dos planos ele tá gerando um embed de esse cara aqui, transformando isso em vetor,
  Confiança: -0.16264240916182354

Segmento 391:
  Início: 3872.8s
  Fim: 3891.8s
  Texto:  e daí fazendo uma busca comparando o vetor gerado daqui com os vetores que estão no banco, tá? Essa que é a grande questão. É que aqui com LinkedIn tá muito bestraído com Framework. Vocês entenderam o nível de abistração que a gente tá aqui?
  Confiança: -0.1921006441116333

Segmento 392:
  Início: 3891.8s
  Fim: 3920.8s
  Texto:  Beleza? Então esse aí que é a grande pegada, tá? E aqui eu fiz um loop bem simples pra trazer os resultados. Eu mandei trazer três resultados aqui e mandei trazer o score. Deixa eu tentar rodar essa parada aqui pra ver se funciona. Não sei se como é que tá a minha conexão com o banco de dados. Se não funcionar beleza, eu vou colocar Python 5, não é, vetoriais, o Python aqui, ForSurch Vector.
  Confiança: -0.25966561282122574

Segmento 393:
  Início: 3920.8s
  Fim: 3944.8s
  Texto:  Vou dar um enter aqui. E agora ele fez a busca agora aqui pra mim. Então, se vocês olharem aqui nas buscas, aconteceu o seguinte. Olha só. Ele pegou um resultado um score 0.35 bem ruimzinho e ele trouxe o texto com o pedaço, tá? Daquele documento PDF que a gente leu.
  Confiança: -0.1706701914469401

Segmento 394:
  Início: 3945.8s
  Fim: 3961.8s
  Texto:  Tá? E os metadados encontrados foram. É a página 7 do documento. O source é esse, o creator foi isso, a data de modificação, producera, etc. Uma quantidade de páginas do PDF para essa, o creation date é isso aqui, etc.
  Confiança: -0.2935017805833083

Segmento 395:
  Início: 3961.8s
  Fim: 3978.8s
  Texto:  Aí eu tenho o segundo texto, o segundo resultado com score 0.36, que foi esse texto aqui com esses metadados aqui. E eu tenho um outro cara que é 0.37 que tem esse outro pedaço de texto aqui pra mim.
  Confiança: -0.170932514326913

Segmento 396:
  Início: 3978.8s
  Fim: 3986.8s
  Texto:  Então, é basicamente isso que acaba acontecendo quando a gente está falando especificamente em reg.
  Confiança: -0.2939675305340741

Segmento 397:
  Início: 3986.8s
  Fim: 3999.8s
  Texto:  Aí, com isso eu iria embedar no meu prompt e na hora que eu embedro no meu prompt o meu a minha, vai conseguir responder. Essa que é a ideia básica de como que funciona essa parada aí.
  Confiança: -0.2939675305340741

Segmento 398:
  Início: 4000.8s
  Fim: 4020.8s
  Texto:  Fechou, galera? Deu pra vocês entenderem. Tem gente perguntona como que estima o custo, etc. Normalmente você tem ferramentas de observabilidade, você vai ter custo para o usuário, ou seja, você vai pegar uma média da quantidade de toque em si quer uma pergunta e a quantidade de toque em si saída e ele vai trazer pra você basicamente o custo.
  Confiança: -0.21660125896494875

Segmento 399:
  Início: 4020.8s
  Fim: 4031.8s
  Texto:  Você pode multiplicar pela quantidade de requisições, tem muitas técnicas e tem ferramentas pra fazer isso, inclusive tem ferramentas pra verificar se os resultados que estão sendo gerados são satisfatórios.
  Confiança: -0.1965865591297979

Segmento 400:
  Início: 4031.8s
  Fim: 4046.8s
  Texto:  Então, isso aí é um ponto importante aí pra você saber. Mas, galera, meu ponto aqui não é profundar e dá aula de régui. Eu queria que vocês entendessem que existe esse conceito e que isso é muito utilizado hoje em dia caso você queira trabalhar com IAA.
  Confiança: -0.1965865591297979

Segmento 401:
  Início: 4046.8s
  Fim: 4065.8s
  Texto:  E lembrando, galera, isso não é só pra chatbot, imagina que eu realmente tenho um processo que vai gerar relatórios e pra eu gerar relatórios eu tenho que pegar resultados, tem que pegar presentações, pega a transcrições de reuniões, preciso compilar tudo isso pra gerar um relatório final pra diretoria, no memória, no executivo.
  Confiança: -0.2088056911121715

Segmento 402:
  Início: 4065.8s
  Fim: 4074.8s
  Texto:  Cara, não tem nada a ver com chatbot, mas ainda assim precisa fazer esses processos, entendeu? Então, é bem essa pegada aí pra vocês, tá?
  Confiança: -0.16498251711384634

Segmento 403:
  Início: 4074.8s
  Fim: 4089.8s
  Texto:  Agora, existem as formas que acabam ficando mais complexas, tá? E tipo, vou dar um exemplo da parada do que eu acho que é o de curso, talvez, eu acho que será que era esse cara aqui.
  Confiança: -0.16498251711384634

Segmento 404:
  Início: 4090.8s
  Fim: 4109.8s
  Texto:  Não acho que não é esse cara, deixa eu tentar lembrar qual esse cara que se eu tenho fácil aqui pra mostrar a MCP, talvez seja esse reggae aqui, talvez seja esse cara.
  Confiança: -0.2508854432539506

Segmento 405:
  Início: 4110.8s
  Fim: 4128.8s
  Texto:  Eu acho que eu posso testar, tá? Python, Scripts, demo, ask questions. Aqui é uma gente que faz perguntas sobre aqueles cursos que estão naquele Google Docs, basicamente é isso, tá?
  Confiança: -0.18873358707801968

Segmento 406:
  Início: 4128.8s
  Fim: 4138.8s
  Texto:  Eu falo Mifal, sobre o MBA em engenharia de software com i-A.
  Confiança: -0.2938170391580333

Segmento 407:
  Início: 4138.8s
  Fim: 4147.8s
  Texto:  Aí o que ele vai fazer? Ele vai, vai pesquisar, etc. Vai gerar o embed, ele vai fazer toda aquela parada e vai trazer o resultado, tá?
  Confiança: -0.2938170391580333

Segmento 408:
  Início: 4147.8s
  Fim: 4157.8s
  Texto:  E aqui ele trouxe pra mim a resposta. Claro, o Mbng era de software, é um curso pensado, ele tendo a razão, os pilares se conectam, funciona assim, funciona assado.
  Confiança: -0.2938170391580333

Segmento 409:
  Início: 4157.8s
  Fim: 4169.8s
  Texto:  Perceba, ele trouxe essa parada. Se eu começar a perguntar de disciplina, ele vai filtrar e vai trazer especificamente de uma disciplina. Se eu faço uma pergunta frequente, ele pega dos documentos de pergunta frequente.
  Confiança: -0.09089879786714594

Segmento 410:
  Início: 4169.8s
  Fim: 4176.8s
  Texto:  Se eu falo qual é a diferença de um curso com outro, eu tenho documentos de comparação de um curso com outro pra ficar mais fácil, entendeu?
  Confiança: -0.09089879786714594

Segmento 411:
  Início: 4176.8s
  Fim: 4186.8s
  Texto:  Então isso aí acaba facilitando aí um pouco a nossa vida. Somente pra vocês saberem que dá pra trabalhar com o Rag de diversas formas, tá?
  Confiança: -0.12155568599700928

Segmento 412:
  Início: 4186.8s
  Fim: 4204.8s
  Texto:  Mas a grande questão aqui, que eu quero que vocês entendam agora, é pra gente ir num outro assunto, que é um assunto que ele ainda é muito confuso com conceitos que normalmente a gente não tá acostumado, tá?
  Confiança: -0.12155568599700928

Segmento 413:
  Início: 4204.8s
  Fim: 4216.8s
  Texto:  Nosso desenvolvedores. Então é o seguinte, a gente vai falar sobre agentes de A, galera.
  Confiança: -0.19959147383527057

Segmento 414:
  Início: 4216.8s
  Fim: 4230.8s
  Texto:  E agentes de A, eu já vou querer trazer aqui de cara pra vocês algumas características desses agentes pra que a gente consiga, vamos dizer, desmistificar final de contas o que é um agente, o que não é um agente.
  Confiança: -0.19959147383527057

Segmento 415:
  Início: 4230.8s
  Fim: 4239.8s
  Texto:  Então, o seguinte, pessoal, a gente já é um software como qualquer outro software. Você faz deploy, ele tem código, é um software.
  Confiança: -0.19483229693244486

Segmento 416:
  Início: 4239.8s
  Fim: 4263.8s
  Texto:  Porém, o core desse software é o LLM. O que que significa que as requestes que esse software recebe, eles vão passar, vamos dizer assim, primariamente, pelo modelo de inteligência artificial, decidir o que ele vai fazer.
  Confiança: -0.18003714712042557

Segmento 417:
  Início: 4264.8s
  Fim: 4271.8s
  Texto:  Sabe quando a gente tem o nosso software comum e entra uma request no API REST e daí você cai ali nas suas regras de negócio?
  Confiança: -0.15109941807199032

Segmento 418:
  Início: 4271.8s
  Fim: 4282.8s
  Texto:  Se o cara faz isso, faz isso, se faz isso, se faz isso, se faz isso, se faz isso, se faz isso, faz isso. A gente faz isso hoje, de uma forma determinística, a gente escolhe como que a gente quer fazer isso.
  Confiança: -0.15109941807199032

Segmento 419:
  Início: 4282.8s
  Fim: 4295.8s
  Texto:  É assim que é o software que a gente desenvolve, o grande ponto é que quando a gente está falando de agente de A, não necessariamente a gente tem esses zifes como nós estamos acostumados a fazer.
  Confiança: -0.185431698958079

Segmento 420:
  Início: 4295.8s
  Fim: 4310.8s
  Texto:  Quem vai definir esses zifes são o modelo de LLM e os promptes que ele recebe. Então, a tomada de decisão do agente vai falar o que deve ser feito.
  Confiança: -0.185431698958079

Segmento 421:
  Início: 4311.8s
  Fim: 4319.8s
  Texto:  Então, as decisões do que vai ser tomado em determinada situação vai ser realizada pela inteligência artificial.
  Confiança: -0.135596866607666

Segmento 422:
  Início: 4319.8s
  Fim: 4329.8s
  Texto:  Por padrão, a inteligência artificial ela não trabalha de forma determinística. É muito claro, cada vez que você faz uma pergunta pra IA, ela responde de uma forma diferente.
  Confiança: -0.135596866607666

Segmento 423:
  Início: 4329.8s
  Fim: 4338.8s
  Texto:  Mesmo que você coloca exatamente o mesmo prompt. Porque ele trabalha com probabilidade, é sempre a probabilidade, a temperatura,
  Confiança: -0.135596866607666

Segmento 424:
  Início: 4338.8s
  Fim: 4351.8s
  Texto:  que vai mudando o resultado da geração que a IA está fazendo. Existem formas que você consegue gerar mais determinismo pra forçar o agente seguido determinado das etapas.
  Confiança: -0.1767819451122749

Segmento 425:
  Início: 4351.8s
  Fim: 4361.8s
  Texto:  Mas o grande ponto de tudo isso é que uma vez que ela trabalha com probabilidades e no final das contas a probabilidade é qual é o próximo toking?
  Confiança: -0.1767819451122749

Segmento 426:
  Início: 4362.8s
  Fim: 4372.8s
  Texto:  E pra isso, ele usa todo o contexto anterior, você nunca de forma geral vai ter o mesmo resultado. Você pode ter comportamento similares.
  Confiança: -0.17020158767700194

Segmento 427:
  Início: 4372.8s
  Fim: 4378.8s
  Texto:  E por isso que você não pode falar zero ou um, normalmente você vai em relação a parte de probabilidades.
  Confiança: -0.17020158767700194

Segmento 428:
  Início: 4379.8s
  Fim: 4393.8s
  Texto:  Agora, o ponto interessante do agente de IA é que ele tem um papel claro, ou seja, normalmente você vai desenvolver um agente que serve exatamente pra resolver um tipo de problema.
  Confiança: -0.1517854170365767

Segmento 429:
  Início: 4394.8s
  Fim: 4408.8s
  Texto:  Haramente você vai fazer um agente que resolve a todos os problemas do mundo. Porque provavelmente ele não consegue cubrir e ter conhecimento e entender no antes de um espectro muito grande.
  Confiança: -0.17535253004594284

Segmento 430:
  Início: 4409.8s
  Fim: 4422.8s
  Texto:  Outra coisa interessante aqui pra gente é que como cada agente ele tem o seu papel claro, esse agente ele também conhece o ambiente que está em volta dele.
  Confiança: -0.11225484872793222

Segmento 431:
  Início: 4422.8s
  Fim: 4435.8s
  Texto:  O que isso significa? Eu sou um agente que faz por request. Mas eu sei que perto de mim eu tenho um agente que escreve o conteúdo da por request.
  Confiança: -0.11225484872793222

Segmento 432:
  Início: 4435.8s
  Fim: 4449.8s
  Texto:  Eu tenho um agente que normalmente verifica se aquele conteúdo está bom. Então ele consegue entender o que tem volta dele e ele tem ferramentas que você dá pra ele utilizar.
  Confiança: -0.10381115936651462

Segmento 433:
  Início: 4449.8s
  Fim: 4463.8s
  Texto:  Por exemplo, um servidor MCP que a gente trabalhou antes, trabalhou ontem, mas não precisa apenas ser uma ferramenta com o MCP. Por exemplo, eu posso dar uma ferramenta como um Google Search pra ele.
  Confiança: -0.13212462338534267

Segmento 434:
  Início: 4463.8s
  Fim: 4475.8s
  Texto:  Eu toda vez que eu perguntar qual é o resultado do jogo do Corinthians duas semanas atrás. Ele vai falar, poxa, eu não sei o resultado do jogo. O que eu tenho de ferramenta?
  Confiança: -0.19124022494541126

Segmento 435:
  Início: 4475.8s
  Fim: 4484.8s
  Texto:  Ah, eu tenho o Google. Bacana. Deixa eu pesquisar no Google qual é o resultado do jogo. Ele pesquisa no Google, pega o resultado e traz pra você.
  Confiança: -0.19124022494541126

Segmento 436:
  Início: 4484.8s
  Fim: 4494.8s
  Texto:  Manjou? Ah, eu quero saber o faturamento da empresa. Opa, eu tenho um servidor MCP aqui que me retorna o faturamento da empresa. Eu vou chamar.
  Confiança: -0.1481762265050134

Segmento 437:
  Início: 4494.8s
  Fim: 4509.8s
  Texto:  Sacou? Então isso aí é importante que vocês consigam entender. Um agente, ele tem um kit do Batman pra ajudar ele resolver os problemas que ele é especialista.
  Confiança: -0.1481762265050134

Segmento 438:
  Início: 4510.8s
  Fim: 4524.8s
  Texto:  Uma coisa interessante é que ele normalmente ele pode se adaptar e improvisar as coisas. Por exemplo, vamos imaginar que eu pergunto um jogo do Corinthians e ele não acha no Google.
  Confiança: -0.16611851178682768

Segmento 439:
  Início: 4524.8s
  Fim: 4536.8s
  Texto:  Mas daí ele fala, poxa, se eu não agente no Google, deixa eu entrar no site da CBF. Qual que era o nome do campeonato? A beleza. Olha, eu não sei, mas eu tenho um amigo aqui que é especialista em futebol.
  Confiança: -0.16611851178682768

Segmento 440:
  Início: 4536.8s
  Fim: 4544.8s
  Texto:  Deixa eu perguntar pra esse outro agente e esse agente responde pra ele. Carou, a gente funciona basicamente como nós ser humanos funcionamos.
  Confiança: -0.15274097238268172

Segmento 441:
  Início: 4544.8s
  Fim: 4551.8s
  Texto:  A gente conhece as coisas que a gente faz, quando a gente não conhece, a gente pergunta pra outras pessoas e vê outras fontes.
  Confiança: -0.15274097238268172

Segmento 442:
  Início: 4551.8s
  Fim: 4565.8s
  Texto:  É basicamente isso que acaba acontecendo, tá? Uma coisa interessante que a gente se consegue trabalhar e que os nossos softwares, por padrão, não consigam trabalhar por padrão, é.
  Confiança: -0.15274097238268172

Segmento 443:
  Início: 4565.8s
  Fim: 4583.8s
  Texto:  Trabalhar com dados desustruturados. Os nossos softwares dependem muito da sua, do resultado da sua API. Vamos imaginar que eu mandei um JSON. Quem nunca quebrou uma API? Porque o dado era inteiro e mandaram como string e deu pau pra você. Entende?
  Confiança: -0.1814431162441478

Segmento 444:
  Início: 4583.8s
  Fim: 4597.8s
  Texto:  Então, uma gente de há mesmo que receba uma informação que não tá tão estruturada, ainda assim ele consegue inferir, entender a noance daquela informação pra tentar fazer o melhor com que ele pode.
  Confiança: -0.27297916014989215

Segmento 445:
  Início: 4597.8s
  Fim: 4613.8s
  Texto:  Sacou? E esses agentes também podem fazer simulações de cenário antes de tomar uma decisão. Uma coisa interessante também é que esses agentes têm memória de curto e longo prazo.
  Confiança: -0.11472607673482692

Segmento 446:
  Início: 4613.8s
  Fim: 4633.8s
  Texto:  Uma memória de curto prazo é uma memória que tá acontecendo durante aquela seção. Um memória de longo prazo é uma memória que, quando alguém pergunta algo pra ele, ele consegue resgatar isso do passado, entender essa informação e usar essa informação pra continuar trabalhando.
  Confiança: -0.09372125038733849

Segmento 447:
  Início: 4633.8s
  Fim: 4651.8s
  Texto:  E existem diversas técnicas pra você trabalhar com memória. É uma outra seara, é uma outra técnica, é um outro formato de arquitetura pra você trabalhar com memória de longo prazo. E fazendo o Jabá, nosso MBA de engenharia de software conyaça, vai aprender muito sobre isso também.
  Confiança: -0.21968601845406197

Segmento 448:
  Início: 4651.8s
  Fim: 4670.8s
  Texto:  Bote o link aí galera da nossa equipe pra vocês conhecerem mais, pessoal. Entrem em contato, a gente tem duas turmas abertas com datas diferentes e que têm condições de pagamentos eventualmente até diferentes inflexibilidades. Então entra em contato com a nossa equipe pra gente, tá bom?
  Confiança: -0.2094117386700356

Segmento 449:
  Início: 4670.8s
  Fim: 4686.8s
  Texto:  Então, o que acontece? E quem é luno da fussaico de qualquer curso também tem desconto, só pra vocês saberem. Então essa é a parada que um agente faz. Agora a gente tem que tirar algumas confusões que acabam acontecendo no meio da história, tá?
  Confiança: -0.21762574139763327

Segmento 450:
  Início: 4686.8s
  Fim: 4691.8s
  Texto:  Eu já ouvi falar algumas coisas desse tipo que eu quero trabalhar pra você.
  Confiança: -0.21762574139763327

Segmento 451:
  Início: 4692.8s
  Fim: 4707.8s
  Texto:  Uma gente não é uma LLM, mais regra de negócio. Uma gente não é um reggu. Um reggu pode ser simplesmente um software, um script que eu fiz pra fazer ingestão ou uma busca no banco de dados.
  Confiança: -0.1738259477435418

Segmento 452:
  Início: 4707.8s
  Fim: 4718.8s
  Texto:  Mas não necessariamente isso é uma gente. Uma gente ele conhece, ele tem especificidade, ele sabe o que ele tem que fazer, ele sabe como é que ele tem que responder, ele sabe as ferramentas que ele tem.
  Confiança: -0.1738259477435418

Segmento 453:
  Início: 4718.8s
  Fim: 4725.8s
  Texto:  Então aquilo que eu fiz pra vocês e me mostrar ali não é um agente de A. Aquilo ali é uma ferramenta que faz busco em vetor.
  Confiança: -0.18293053476434004

Segmento 454:
  Início: 4725.8s
  Fim: 4727.8s
  Texto:  Sacou qualquer diferença?
  Confiança: -0.18293053476434004

Segmento 455:
  Início: 4727.8s
  Fim: 4740.8s
  Texto:  MCP não é um agente. É um protocolo que um agente pode consultar. Um agente não é um chatbot, tá? Não significa que eu não posso ter um chatbot que é um agente.
  Confiança: -0.18293053476434004

Segmento 456:
  Início: 4740.8s
  Fim: 4749.8s
  Texto:  Mas nem todo chatbot é um agente de A. E a gente tem que tomar muito cuidado com isso, porque hoje em dia falo, ali eu tenho um chatbot, etc.
  Confiança: -0.1919748394988304

Segmento 457:
  Início: 4749.8s
  Fim: 4759.8s
  Texto:  E esse chatbot não use a em picas nenhuma. Entendeu? Ou seja, um chatbot pode ser um agente de A. Mas nem todo chatbot é um agente de A.
  Confiança: -0.1919748394988304

Segmento 458:
  Início: 4759.8s
  Fim: 4769.8s
  Texto:  É importante que vocês consigam entender isso, porque a chatbot ele tem um modifielse e daí você vai clicando, ele vai te navegando e coisas desse tipo.
  Confiança: -0.1414688783533433

Segmento 459:
  Início: 4769.8s
  Fim: 4779.8s
  Texto:  Muito diferente de ser um agente que consegue tomar uma decisão. Por exemplo, posso entrar no chat e falar, olha, eu não gostei desse pedido do último pedido que eu fiz.
  Confiança: -0.1414688783533433

Segmento 460:
  Início: 4779.8s
  Fim: 4791.8s
  Texto:  Na aí o chatbot vai lá consulta no último pedido que o cara fez. Ó, se eu vi que você comprou as baquetas a 5A pra você tocar bateria, você realmente não gostou dela. Por quê?
  Confiança: -0.19412687991527802

Segmento 461:
  Início: 4791.8s
  Fim: 4798.8s
  Texto:  Ai, eu não gostei por isso e por isso. Nossa, eu sinto muito. Olha, se você quiser, você pode fazer devolução, certo? Você gostaria?
  Confiança: -0.19412687991527802

Segmento 462:
  Início: 4798.8s
  Fim: 4810.8s
  Texto:  Sim, então faz a devolução pra mim. Neste momento, o agente vai lá, entro com pedido de devolução, adiciona observação no sistema que o cliente não gostou da baqueta pela qualidade na hora que ele foi tocar.
  Confiança: -0.2386211253978588

Segmento 463:
  Início: 4810.8s
  Fim: 4822.8s
  Texto:  A notóqua foi o cliente, entrou no CRI, me anotou no CRI, me cês cliente, já comprou a baqueta e não gostou daquela marca. E daí ele falou, olha, o seu pedido já foi ali embossado, você vai receber um e-mail.
  Confiança: -0.2386211253978588

Segmento 464:
  Início: 4822.8s
  Fim: 4836.8s
  Texto:  Você consegue perceber o que é um agente de inteligência artificial, consegue fazer tudo com linguagem natural, ele consegue consultar, ele consegue dar baixa impedido, ele consegue fazer uma venda, ele consegue fazer uma compra, ele consegue ajudar.
  Confiança: -0.1534258065764437

Segmento 465:
  Início: 4836.8s
  Fim: 4846.8s
  Texto:  Então, tudo isso você não consegue resolver com o e-fiels, entendeu? Você não consegue resolver com o e-fiel, você ainda mais conversando em linguagem natural.
  Confiança: -0.1534258065764437

Segmento 466:
  Início: 4846.8s
  Fim: 4867.8s
  Texto:  Então, é totalmente possível, vou fazer isso. E o agente pode ser configurado da seguinte forma ainda, eu posso falar, cara, e assim que você fizer em em bolso, verifique se tem um produto similar, e ainda ofereça pra ele, olha, eu vi que você pediu a em bolsa baqueta, mas existe essa baqueta aqui também, que está com um rating de cinco estrelas, você não quer conhecer?
  Confiança: -0.2517566873569681

Segmento 467:
  Início: 4867.8s
  Fim: 4880.8s
  Texto:  É essa aqui, eu acho que eu gostei, posso fazer a compra pra você, ainda te dão um desconto e eu consigo te entregar pra manhã, pode, então tudo bem, o próprio agente vai lá, faz a ordem, faz o pedido, ajusta o frete, tudo mais, e acabou por ali.
  Confiança: -0.18260520382931358

Segmento 468:
  Início: 4880.8s
  Fim: 4895.8s
  Texto:  Sem entender a capacidade que a gente tem com a gente, e vocês conseguiram entender a diferença de uma gente de A, do que um software bem simples, que usa, E A, é muita diferença.
  Confiança: -0.19835033822566905

Segmento 469:
  Início: 4895.8s
  Fim: 4909.8s
  Texto:  Saco, é muita diferença mesmo de uma gente de A, por isso que eu não vou dizer que eu fico bravo, mas quando alguém fala que a gente de A e A chequebote eu quero morrer, porque você não precisa estar falando com o usuário final, entendeu?
  Confiança: -0.2499668708214393

Segmento 470:
  Início: 4909.8s
  Fim: 4933.8s
  Texto:  Eu posso ter agentes que trabalham no meu processo de logística, a um produto saiu defeituoso, a gente entende o produto, consegue perceber onde está as falhas, daí ele manda pra um outro agente que reporta a área de qualidade que a falha está dessa forma, que manda pra um outro agente pra área de engenharia poder revisar essa falha, e já agera ainda uma tarefa pro engenheiro rever aquilo.
  Confiança: -0.13625610896519252

Segmento 471:
  Início: 4933.8s
  Fim: 4939.8s
  Texto:  Cara, perceba que tudo isso é possível, e não é chequebote, entendeu?
  Confiança: -0.19975728626492656

Segmento 472:
  Início: 4941.8s
  Fim: 4955.8s
  Texto:  E aí o grande ponto, como eu falei, agentes, eles têm especialidades, e é por isso que hoje em dia, a coisa mais comum do mundo, é você trabalhar com aplicações multia-genticas ou multia-gentes.
  Confiança: -0.19975728626492656

Segmento 473:
  Início: 4955.8s
  Fim: 4964.8s
  Texto:  Ou seja, cada agente seu tem uma tarefa, ou ela tem um objetivo, capacidades diferentes.
  Confiança: -0.1683538184975678

Segmento 474:
  Início: 4964.8s
  Fim: 4973.8s
  Texto:  Esses agentes eles podem delegar responsabilidades. Esses agentes podem usar outros agentes como ferramenta.
  Confiança: -0.1683538184975678

Segmento 475:
  Início: 4973.8s
  Fim: 4982.8s
  Texto:  Esses agentes podem ter um agente orquestrador que fica recebendo, faz isso o meu amigo, agora você faz isso, eu pego esse resultado,
  Confiança: -0.16335155924812692

Segmento 476:
  Início: 4982.8s
  Fim: 4991.8s
  Texto:  agora eu mando para o outro, tipo um chefe que vai pedindo para o monte de gente fazer as coisas, cada um vai entregando, ele compila tudo e retorna para a diretoria.
  Confiança: -0.16335155924812692

Segmento 477:
  Início: 4991.8s
  Fim: 5002.8s
  Texto:  Tipo isso, sacou? Uma coisa louca é que quando um agente responde o outro, você pode responder como se fosse um humano falando com outro humano.
  Confiança: -0.16335155924812692

Segmento 478:
  Início: 5003.8s
  Fim: 5014.8s
  Texto:  Entendeu? E os agentes, eles podem compartilhar estado, ou seja, eu posso ver, eu pedi para você, por exemplo, gerar um relatório.
  Confiança: -0.13802266120910645

Segmento 479:
  Início: 5014.8s
  Fim: 5027.8s
  Texto:  Eu, como outro, a gente, posso ver qual que é o status do seu relatório, da sua geração de relatório, porque você pode compartilhar comigo o seu estado naquela tarefa que você está fazendo.
  Confiança: -0.13802266120910645

Segmento 480:
  Início: 5027.8s
  Fim: 5036.8s
  Texto:  E o relatório, você pode gerar um documento num bucket lá do Google, por exemplo, como um artefato onde eu vou ter acesso para eu poder baixar.
  Confiança: -0.11806752708520782

Segmento 481:
  Início: 5036.8s
  Fim: 5049.8s
  Texto:  Sacou? Então, é muito louco esse tipo de coisa. E no meio dessa história, a gente tem um monte de framework, um monte de biblioteca, que você consegue usar para criar agentes de A.
  Confiança: -0.11806752708520782

Segmento 482:
  Início: 5049.8s
  Fim: 5059.8s
  Texto:  Então, sim, você consegue criar agentes de A usando Lang Chen. Dá para você criar tranquilamente agentes de A utilizando Lang Chen.
  Confiança: -0.16393329672617454

Segmento 483:
  Início: 5059.8s
  Fim: 5067.8s
  Texto:  O ponto é que, se você for usar Lang Chen para criar um agente de A, provavelmente esse agente de A, ele vai ter, ele vai ser mais simples.
  Confiança: -0.16393329672617454

Segmento 484:
  Início: 5067.8s
  Fim: 5080.8s
  Texto:  Vai ser mais fácil, mas vai ser mais simples. Trabalhar de formas muito complexas, com muitas responsabilidades e coisas desse tipo, podem começar a ficar insustentável trabalhar com Lang Chen pura.
  Confiança: -0.1336959905402605

Segmento 485:
  Início: 5080.8s
  Fim: 5091.8s
  Texto:  Por isso que a gente tem outros frameworks, como o Lang Graph, que usa Lang Chen, para criar basicamente fluxos de como que os agentes trabalham em conjunto.
  Confiança: -0.1336959905402605

Segmento 486:
  Início: 5091.8s
  Fim: 5104.8s
  Texto:  Você tem o Crew AI, que é uma outra forma que você, como se tem uma equipe de agentes, que cada um pega as tarefas, cada um separa umas tarefas, nos caras vão fazer nas tarefas juntos até terminar.
  Confiança: -0.19773735434322035

Segmento 487:
  Início: 5104.8s
  Fim: 5110.8s
  Texto:  Tem outro, a gente que chama Google ADK, que é o que eu vou falar para vocês hoje.
  Confiança: -0.24105405807495117

Segmento 488:
  Início: 5110.8s
  Fim: 5117.8s
  Texto:  Google ADK é o agent de development kit da Google, ele rodou por um ano em produção na Google antes de ser disponibilizado o Open Source.
  Confiança: -0.24105405807495117

Segmento 489:
  Início: 5117.8s
  Fim: 5127.8s
  Texto:  É um projeto que eu gosto muito, ele é bem completo, porque eu consigo trabalhar com ele de forma determinística e não determinística.
  Confiança: -0.24105405807495117

Segmento 490:
  Início: 5127.8s
  Fim: 5138.8s
  Texto:  E ele tem bastante coisa bacana para trabalhar e inclusive vai ser sobre o ADK que a gente vai falar hoje aqui.
  Confiança: -0.2542303475466641

Segmento 491:
  Início: 5138.8s
  Fim: 5141.8s
  Texto:  Então nesse momento, deixe sobe a beber uma água.
  Confiança: -0.2542303475466641

Segmento 492:
  Início: 5141.8s
  Fim: 5156.8s
  Texto:  O Gilson está falando aqui para mim, me perguntou, uma gente pode substituir um telemárqueting e eu vou falar que sim, tá Gilson?
  Confiança: -0.25286569595336916

Segmento 493:
  Início: 5156.8s
  Fim: 5161.8s
  Texto:  E eu vou falar que sim, com ganho, com experiência prática.
  Confiança: -0.****************

Segmento 494:
  Início: 5161.8s
  Fim: 5172.8s
  Texto:  Esses dias eu recebi uma ligação do meu banco e a primeira palavra que falou foi,
  Confiança: -0.****************

Segmento 495:
  Início: 5172.8s
  Fim: 5182.8s
  Texto:  somente para você saber nesse momento a sua ligação está sendo gravada e a interação que você vai estar tendo aqui é com uma gente de inteligência artificial.
  Confiança: -0.****************

Segmento 496:
  Início: 5182.8s
  Fim: 5187.8s
  Texto:  E depois, em seguida, começou a falar assim, e aí o Eslet, tudo bem?
  Confiança: -0.*****************

Segmento 497:
  Início: 5187.8s
  Fim: 5188.8s
  Texto:  Sim, tudo bem, tal.
  Confiança: -0.*****************

Segmento 498:
  Início: 5188.8s
  Fim: 5196.8s
  Texto:  Opa, eu sou do banco, tal, até onde você tem o financiamento da sua casa e a gente está mudando o internet banking.
  Confiança: -0.*****************

Segmento 499:
  Início: 5196.8s
  Fim: 5202.8s
  Texto:  E eu queria saber se você recebeu e-mail com os novos dados de acesso do internet banking.
  Confiança: -0.*****************

Segmento 500:
  Início: 5202.8s
  Fim: 5209.8s
  Texto:  Eu falei, não, eu não recebi não. Quando você mandou, ele fala, cara, a gente mandou tal dia e o assunto foi esse.
  Confiança: -0.*****************

Segmento 501:
  Início: 5209.8s
  Fim: 5215.8s
  Texto:  Eu falo, ah, legal, eu vou dar um molhado. Bom, se você quiser, eu reenviu agora para você. Pode ser, pode.
  Confiança: -0.*****************

Segmento 502:
  Início: 5215.8s
  Fim: 5223.8s
  Texto:  A tota ir reenviando. Beleza, ah, recebi aqui. Ah, legal, beleza. Então, é só se clicar a funciona dessa forma, se tiver, etc.
  Confiança: -0.*****************

Segmento 503:
  Início: 5223.8s
  Fim: 5229.8s
  Texto:  Tem mais alguma coisa que eu consigo te ajudar, Wesley, alguma dúvida, alguma coisa em relação a isso, o financiamento, etc.
  Confiança: -0.*****************

Segmento 504:
  Início: 5229.8s
  Fim: 5232.8s
  Texto:  Aí eu falei, não, então tudo bem, bota a ed, etc.
  Confiança: -0.*****************

Segmento 505:
  Início: 5232.8s
  Fim: 5240.8s
  Texto:  Cara, o que eu estou dizendo com isso é que se não tivesse sido falado, no início, que era um agente de a falando comigo,
  Confiança: -0.17126058809684985

Segmento 506:
  Início: 5240.8s
  Fim: 5251.8s
  Texto:  eu jamais, em hipótesi alguma, eu saberia que eu estaria falando com a gente, porque era um humano falando.
  Confiança: -0.17126058809684985

Segmento 507:
  Início: 5251.8s
  Fim: 5258.8s
  Texto:  Não tinha lag, era super natural, com intonação, tudo que ele falava, eu recebi o e-mail na hora.
  Confiança: -0.22167821274590246

Segmento 508:
  Início: 5258.8s
  Fim: 5266.8s
  Texto:  Cara, era imperceptível. Eu só consegui descobrir por conta que foi falado o início.
  Confiança: -0.22167821274590246

Segmento 509:
  Início: 5266.8s
  Fim: 5268.8s
  Texto:  Entendeu?
  Confiança: -0.22167821274590246

Segmento 510:
  Início: 5270.8s
  Fim: 5279.8s
  Texto:  Manjou, galera, então, assim, dá para assim um agente substituir um telemárquete com certeza da que para frente vai dar.
  Confiança: -0.22167821274590246

Segmento 511:
  Início: 5279.8s
  Fim: 5291.8s
  Texto:  E é muito louco essa parada aí, tá? Agora o lance é o seguinte, pessoal, vamos falar sobre o Google ADK.
  Confiança: -0.19526733670915877

Segmento 512:
  Início: 5291.8s
  Fim: 5297.8s
  Texto:  O Google ADK é um agente developement kit da Google.
  Confiança: -0.19526733670915877

Segmento 513:
  Início: 5297.8s
  Fim: 5307.8s
  Texto:  A ideia do ADK é que você tenha um agente base, vamos dizer assim, e esse agente base vamos dizer que ele é uma interface.
  Confiança: -0.19526733670915877

Segmento 514:
  Início: 5308.8s
  Fim: 5317.8s
  Texto:  E baseado nesse agente base, eu tenho tipos diferentes de trabalho, que são agent types.
  Confiança: -0.12861852212385697

Segmento 515:
  Início: 5317.8s
  Fim: 5323.8s
  Texto:  O primeiro tipo de agent type é um agente baseado em LLM.
  Confiança: -0.12861852212385697

Segmento 516:
  Início: 5323.8s
  Fim: 5331.8s
  Texto:  O que que significa, galera? O que que significa? Significa que você manda uma requisição para esse agente,
  Confiança: -0.12861852212385697

Segmento 517:
  Início: 5331.8s
  Fim: 5334.8s
  Texto:  ele vai executar um modelo de inteligência artificial e vai responder.
  Confiança: -0.12861852212385697

Segmento 518:
  Início: 5335.8s
  Fim: 5349.8s
  Texto:  Eu tenho outros tipos de agente que são agentes de workflow, ou seja, eu posso criar vários agentes e eu posso pedir que cada agente siga um fluxo, um atrás do outro.
  Confiança: -0.19923350275779256

Segmento 519:
  Início: 5349.8s
  Fim: 5357.8s
  Texto:  Ou eu posso fazer que eles façam coisas em paralela, ou eu posso pedir para insificar em loop até eles resolveram um problema,
  Confiança: -0.19923350275779256

Segmento 520:
  Início: 5357.8s
  Fim: 5362.8s
  Texto:  ou eu posso criar um comportamento completamente diferente e criar um meu agente customizado.
  Confiança: -0.19923350275779256

Segmento 521:
  Início: 5363.8s
  Fim: 5372.8s
  Texto:  Fez sentido para vocês, pessoal, a ideia principal desse Google, a devvelopement kit, então é basicamente essa história.
  Confiança: -0.20592365086635697

Segmento 522:
  Início: 5372.8s
  Fim: 5377.8s
  Texto:  Então, o que acontece? Ele tem alguns componentes aqui que eu queria trazer a explicar aqui para vocês.
  Confiança: -0.20592365086635697

Segmento 523:
  Início: 5377.8s
  Fim: 5383.8s
  Texto:  Então, o primeiro componente, eu acabei falando já agora aqui, que são os tipos de agente.
  Confiança: -0.20592365086635697

Segmento 524:
  Início: 5383.8s
  Fim: 5389.8s
  Texto:  Eu tenho base agent, eu tenho LLM agent e eu tenho os agentes que eu trabalho de orchestração.
  Confiança: -0.20592365086635697

Segmento 525:
  Início: 5390.8s
  Fim: 5399.8s
  Texto:  E, se madristo, os meus agentes têm tuos que são ferramentas, ou seja, essas tuos podem ser ok.
  Confiança: -0.18247442329879357

Segmento 526:
  Início: 5399.8s
  Fim: 5405.8s
  Texto:  Eu posso criar uma função na minha aplicação e fazer essa função virar uma ferramenta.
  Confiança: -0.18247442329879357

Segmento 527:
  Início: 5405.8s
  Fim: 5409.8s
  Texto:  Vamos imaginar que eu crie uma função que é criar um novo pedido.
  Confiança: -0.18247442329879357

Segmento 528:
  Início: 5409.8s
  Fim: 5417.8s
  Texto:  Aí, eu dou essa ferramenta para o agente e toda vez que alguém pediu para ele criar um novo pedido, ele vai ver a tu e ele vai falar,
  Confiança: -0.18247442329879357

Segmento 529:
  Início: 5417.8s
  Fim: 5422.8s
  Texto:  opa, eu tenho uma ferramenta de criar pedidos. Então, eu vou usar essa ferramenta pelo criar o pedido, basicamente isso.
  Confiança: -0.1331245248967951

Segmento 530:
  Início: 5422.8s
  Fim: 5430.8s
  Texto:  Eu tenho uma outra parada que é chamado de Agent Tool, ou seja, é quando eu pego um outro agente que eu conheço,
  Confiança: -0.1331245248967951

Segmento 531:
  Início: 5430.8s
  Fim: 5433.8s
  Texto:  e uso ele como se fosse uma ferramenta minha.
  Confiança: -0.1331245248967951

Segmento 532:
  Início: 5433.8s
  Fim: 5445.8s
  Texto:  Mas, quando eu uso ele como ferramenta, esse agente pode ter as tuos deles que ele vai usar essas tuos para resolver o problema para trazer para o agente principal que está usando ele como ferramenta.
  Confiança: -0.1331245248967951

Segmento 533:
  Início: 5445.8s
  Fim: 5447.8s
  Texto:  Entendeu qualquer ideia, pessoal?
  Confiança: -0.18973850674099393

Segmento 534:
  Início: 5447.8s
  Fim: 5452.8s
  Texto:  E eu tenho, ali, também, tuos de suporte.
  Confiança: -0.18973850674099393

Segmento 535:
  Início: 5452.8s
  Fim: 5461.8s
  Texto:  Ou seja, são ferramentas que vão me ajudar a trabalhar, por exemplo, integrações que eu posso ter com outros sistemas,
  Confiança: -0.18973850674099393

Segmento 536:
  Início: 5461.8s
  Fim: 5470.8s
  Texto:  eu posso ter especificações que a OpenAI consegue usar, e eu posso criar essas ferramentas baseadas nela com Function Tool Calling,
  Confiança: -0.18973850674099393

Segmento 537:
  Início: 5470.8s
  Fim: 5478.8s
  Texto:  eu posso trabalhar com MCPS, então tudo isso são possibilidades que a gente pode inter, para eles saberem o que eles podem fazer.
  Confiança: -0.19302015526350155

Segmento 538:
  Início: 5478.8s
  Fim: 5481.8s
  Texto:  Então, essa é a grande pegada.
  Confiança: -0.19302015526350155

Segmento 539:
  Início: 5481.8s
  Fim: 5488.8s
  Texto:  Outra coisa que os agentes têm, galera, cessões, sessions.
  Confiança: -0.19302015526350155

Segmento 540:
  Início: 5488.8s
  Fim: 5493.8s
  Texto:  O que é uma seção, galera? É o histórico cronológico de eventos.
  Confiança: -0.19302015526350155

Segmento 541:
  Início: 5493.8s
  Fim: 5504.8s
  Texto:  Cada vez que você instiga uma conversa, você está iniciando uma nova seção, e cada conversação, cada mensagem que acaba entrando, vira um histórico daquela seção.
  Confiança: -0.15194379872289196

Segmento 542:
  Início: 5504.8s
  Fim: 5519.8s
  Texto:  E aquele histórico de acordo com o que é falado, vai sendo gravado em um state, que é o estado da conversa, é o momento que está acontecendo aquela operação.
  Confiança: -0.15194379872289196

Segmento 543:
  Início: 5519.8s
  Fim: 5523.8s
  Texto:  O que ele tem também são artefatos nessas seções.
  Confiança: -0.1818880586542635

Segmento 544:
  Início: 5523.8s
  Fim: 5534.8s
  Texto:  Às vezes eu falo pro agente, ele me gera um PDF, e eu gero esse arquivo binário como um artefato, e eu posso usar esse artefato depois para mandar um link ou para passar para outro agente.
  Confiança: -0.1818880586542635

Segmento 545:
  Início: 5534.8s
  Fim: 5548.8s
  Texto:  E as seções também isolam conversas, ou seja, eu consigo criar diversas seções simultâneas, da mesma forma como eu consigo criar vários chats quando eu estou falando com o GPT.
  Confiança: -0.1818880586542635

Segmento 546:
  Início: 5548.8s
  Fim: 5554.8s
  Texto:  E quando a seção é uma nova seção, cada seção tem um ID. E quando se abre um website, você gera uma seção.
  Confiança: -0.36068932906441065

Segmento 547:
  Início: 5554.8s
  Fim: 5556.8s
  Texto:  Basicamente isso.
  Confiança: -0.36068932906441065

Segmento 548:
  Início: 5556.8s
  Fim: 5566.8s
  Texto:  Outra coisa que o ADK também tem como componente, é a parte de memória. Então o que isso significa?
  Confiança: -0.36068932906441065

Segmento 549:
  Início: 5566.8s
  Fim: 5581.8s
  Texto:  Se significa que ele tem a memória curto-prásio, que é o state da seção atual, que é o que ele precisa para continuar conversa e com os resultados dos processamentos dele, de tudo o que ele fez, e ele tem também memória de longo prazo.
  Confiança: -0.22389561579777645

Segmento 550:
  Início: 5582.8s
  Fim: 5593.8s
  Texto:  Que é um memore-service, onde eu posso ter um memore-service em memória, mas cair da sua aplicação, você perde todo o histórico, logo é feito muito mais para test.
  Confiança: -0.25251680612564087

Segmento 551:
  Início: 5593.8s
  Fim: 5606.8s
  Texto:  E você tem outras formas de gravar memória, inclusive serviços do Vertex AI, cara, que é uma opinião dos melhores ecossistemas de serviços para a inteligência artificial, que é da Google,
  Confiança: -0.25251680612564087

Segmento 552:
  Início: 5606.8s
  Fim: 5613.8s
  Texto:  que ele tem próprio ali, inclusive, para você conseguir guardar o histórico de todas as seções.
  Confiança: -0.25841204325358075

Segmento 553:
  Início: 5613.8s
  Fim: 5623.8s
  Texto:  Inclusive, ele tem banco de dados de vetor com foco em Rags, já que já consegue trazer todo esse histórico, que não é o que você foi trabalhar com a gente. É muito louco, tá?
  Confiança: -0.25841204325358075

Segmento 554:
  Início: 5624.8s
  Fim: 5651.8s
  Texto:  Outra coisa também, que eu já falei, mas é uma entidade aqui no ADK, que são os artefatos. Então são arquivos binários nomeados e versionados, o versionamento dele é automático, eu consigo colocar diversos tipos, eu posso falar com PDF, eu posso falar com um doc, eu posso falar com TXT, e ele tem uma integração nativa com GCS, que é o Google Cloud Storage, que é basicamente o S3 do Google.
  Confiança: -0.18647951500438084

Segmento 555:
  Início: 5651.8s
  Fim: 5675.8s
  Texto:  Então você consegue subir e guardar esses artefatos ali também, beleza? Então, como é que funciona somente para a gente ter uma visão geral do ADK aqui no nosso caso, pessoal? Funciona mais ou menos assim, deixa eu copiar aqui, colar para ficar mais fácil, que eu criei uma imagensinha bonitinha aqui para vocês, com todo carinho e amor para vocês, tá?
  Confiança: -0.1482668230610509

Segmento 556:
  Início: 5675.8s
  Fim: 5693.8s
  Texto:  Então ele funciona mais ou menos assim quando você olha o ADK para você entender a estrutura dele. Eu tenho usuário que pode ser um sistema, um outro a gente ou qualquer coisa, ele tem um runner, runner é aquele cara que instancia o processo para começar a execução do a gente.
  Confiança: -0.1663343400666208

Segmento 557:
  Início: 5693.8s
  Fim: 5715.8s
  Texto:  Para você usar um runner, você vai ter que instanciar uma seção para você ter o STATE menigmente, ou seja, para guardar o estado da seção, você tem que ter um membro de service para você guardar e armazenar a memória, a longo ou curto prazo, e você vai ter que ter um artefact service para dizer onde você vai armazenar os dados que foram gerados com o artefatos.
  Confiança: -0.23192420232863653

Segmento 558:
  Início: 5715.8s
  Fim: 5729.8s
  Texto:  Baseado nisso, você vai ter com runner a instanciação dos seus agentes onde você define esses agentes que eles vão trabalhar. Quando você define esses agentes, você fala quais são as ferramentas que esses agentes têm.
  Confiança: -0.11949312119256883

Segmento 559:
  Início: 5729.8s
  Fim: 5744.8s
  Texto:  E muitas vezes esses agentes eles também conseguem através dessas tools chamar sistemas externos. Então é basicamente assim de uma forma bem grosseira, mas que é bem a realidade quando você quiser entender um pouco mais sobre o ADK,
  Confiança: -0.11949312119256883

Segmento 560:
  Início: 5744.8s
  Fim: 5762.8s
  Texto:  é mais ou menos assim que ele funciona. Então se a gente fosse falar da interação dos componentes, isso aqui um pouco mais baixo nível, mas eu vou citar, mas é mais baixo nível. Então eu tenho uma input do usuário, eu vou dar um runner, o runner vai criar um invocação context para ele ter o contexto,
  Confiança: -0.16262921534086527

Segmento 561:
  Início: 5762.8s
  Fim: 5785.8s
  Texto:  aí ele vai executar de forma cíncrona, o agente para receber o contexto, o agente vai chamar a chamada utilizando o context, aí as tools vão modificar os custat, os artefatos, o agente vai gerar os eventos que foram produzidos pelas modificações, e o session service vai persistir essas mudanças que acontecem nesses eventos para a gente.
  Confiança: -0.22139786168148642

Segmento 562:
  Início: 5785.8s
  Fim: 5794.8s
  Texto:  É basicamente isso que eu estou trazendo isso aqui para vocês, tá galera? É basicamente isso aqui.
  Confiança: -0.12370352391843442

Segmento 563:
  Início: 5794.8s
  Fim: 5808.8s
  Texto:  Agora uma coisa que é importante você entender, principalmente na ideia do ADK, é que nós temos conceitos de agentes e subaigentes, tá? Então o que que acontece?
  Confiança: -0.22388245338617369

Segmento 564:
  Início: 5808.8s
  Fim: 5833.8s
  Texto:  A gente verso o subagente, o conceito principal é que existe uma hierarquia definida via parâmetro chamado subagentes, ou seja, eu tenho um agente pai, e um agente pai, e um agente pode ter apenas um pai, basicamente isso, imagina uma árvore de dependências onde o filho tem um agente pai, e ele não pode ter mais de um pai, basicamente é isso, tá?
  Confiança: -0.15202509880065918

Segmento 565:
  Início: 5834.8s
  Fim: 5848.8s
  Texto:  Qual que é a diferença prática de um agente para um subagente? Então isso aqui é importante, eu vou mostrar para vocês exemplos disso aí, para vocês ficarem um pouco mais claros assim, olhando até em código, tá?
  Confiança: -0.14587536879948207

Segmento 566:
  Início: 5848.8s
  Fim: 5872.8s
  Texto:  A diferença prática é a autonomia, porque o agente principal consegue executar as coisas de forma independente ao tono, ele decide quando e como executar as suas tarefas, e ele pode iniciar conversas e processos por conta própria, ou seja, ele é o cara, beleza?
  Confiança: -0.17853017414317413

Segmento 567:
  Início: 5872.8s
  Fim: 5882.8s
  Texto:  Como que eu consigo dar um exemplo aqui para vocês? Deixa eu ver que eu sei que eu tenho um exemplo aqui que eu consigo dar aqui, e de que é simpel, e a gente aqui que eu separei para vocês, tá?
  Confiança: -0.25988143574107775

Segmento 568:
  Início: 5883.8s
  Fim: 5906.8s
  Texto:  seguinte, tá? Vou dar um exemplo muito bobo de um agente aqui para vocês entenderem, cria um agente chamado Doc Finder, ele é um arquivo.py, olha a galera como é que a definição muito assim, ridícula de um agente, obviamente que as coisas ficam muito mais complexas, não é hora que você vai criar softwares e tudo mais, né?
  Confiança: -0.22844530741373698

Segmento 569:
  Início: 5906.8s
  Fim: 5935.8s
  Texto:  Mas basicamente, eu estou falando qual é o modelo que eu vou utilizar, e eu estou instanciando um agente, eu estou falando que o nome do agente é Doc Finder, e a gente, eu estou dando uma descrição, que esse usuário, ele faz busca técnica de documentos e retorna os documentos mais relevantes, eu estou falando o modelo e aqui eu estou dando a instrução, ou seja, eu estou falando o que ele é, eu estou dando, vamos dizer, o meu sistema, a instrução,
  Confiança: -0.17735947681074382

Segmento 570:
  Início: 5935.8s
  Fim: 5959.8s
  Texto:  eu estou colocando assim para ele, você é um assistente especializado em contrato documentação técnica sobre tecnologias, e é importante você apenas pode responder perguntas sobre tecnologias, frameworks, linguagens, etc., logo você precisa garantir que você nunca responderá absolutamente nada fora do escloco, como? Qual a capital do Brasil? Qual o futuramento da empresa tal?
  Confiança: -0.31243808970731846

Segmento 571:
  Início: 5959.8s
  Fim: 5989.8s
  Texto:  Então, o primeiro passo, analisa se a pergunta sobre esse tecnologia, segundo passo, se você concluir que a pergunta não é sobre tecnologia, responda, desculpe, sou especializado apenas em tecnologia, caso a pergunta seja sobre tecnologia, você deve usar uma ferramenta do Google Search para encontrar documentação da internet, você deve retorna a informação encontrada, informar-te estruturado, siti sempre as fontes encontradas, aqui são as ferramentas chamadas Google Search, que é uma ferramenta nativa, que o ADK tem, e eu tenho um alt put
  Confiança: -0.19869450789231521

Segmento 572:
  Início: 5989.8s
  Fim: 6017.8s
  Texto:  e que é answer, então quando eu executo aqui esse agente, o que vai acontecer aqui para mim é que ele vai fazer a consulta do Google, e daí ele vai trazer o resultado ali para mim, tá? Então, é basicamente isso que é uma gente de uma forma bem, bem simples, sendo que essa ferramenta do Google aqui é uma ferramenta que o ADK tem nativa para fazer Google Search, entendeu?
  Confiança: -0.17136232058207193

Segmento 573:
  Início: 6018.8s
  Fim: 6040.8s
  Texto:  Então, essa é a grande pegada aqui para vocês, maravilha? Deu para vocês entenderem a ideia básica da definição de uma gente, isso é definição de uma gente, só que para vocês entenderem qual é a ideia de você criar uma gente, e perceba que o PROMPT é uma das coisas mais importantes aqui para vocês conseguiram trabalhar, tá?
  Confiança: -0.22275357019333614

Segmento 574:
  Início: 6040.8s
  Fim: 6061.8s
  Texto:  Aí, o que que acontece aqui é o seguinte, lembra que eu falei para vocês que a diferença de um agente pai para os outros é a autonomia, então eu vou dar um exemplo aqui para vocês de exemplos de estrutura de agentes de autonomia, então olha só aqui para vocês entenderem,
  Confiança: -0.11749926857326341

Segmento 575:
  Início: 6061.8s
  Fim: 6074.8s
  Texto:  imagina que eu tenho aqui um agente principal chamado de customer services agent, tá? Então ele é um customer service agent e o nome dele é customer service, tá?
  Confiança: -0.19798736083201873

Segmento 576:
  Início: 6075.8s
  Fim: 6094.8s
  Texto:  maravilha, e aqui eu estou definindo dois subagentes, uma gente chama OrderLOOKUP agent, o nome dele é OrderLOOKUP, e ele tem um outro a gente chamado Refund agent, que ele é um processador de fazer rainbows, legal?
  Confiança: -0.25317845579053533

Segmento 577:
  Início: 6095.8s
  Fim: 6113.8s
  Texto:  E aí, o que que eu vou fazer aqui para mim? Eu estou instanciando o meu customer agent, falando que o nome dele é customer service e falando que ele tem dois subagentes, então com essa autonomia, o que isso significa galera?
  Confiança: -0.1607934550235146

Segmento 578:
  Início: 6113.8s
  Fim: 6133.8s
  Texto:  Significa o seguinte, significa que se eu chegar ali para o cliente e um cliente perguntar ali para mim, olha, eu queria saber meu último produto, esse agente de customer service vai falar quais agentes subagentes que eu tenho aqui para trabalhar para mim resolver esse meu problema,
  Confiança: -0.10523916391225961

Segmento 579:
  Início: 6133.8s
  Fim: 6159.8s
  Texto:  ah, eu tenho um agente que faz verificação de ordens de serviço, aí ele manda ali para o Order agent, Order agent pega essa pergunta desse cliente, tá? E verifique a ordem de serviço para mim, aí esse agente aqui vai consultar e vai responder para esse agente os dados do pedido, que esse agente fez, beleza?
  Confiança: -0.14826400981229895

Segmento 580:
  Início: 6160.8s
  Fim: 6187.8s
  Texto:  Maravilha, fez sentido isso aí para vocês galera? Então, o que que acontece aqui para a gente? Que é o seguinte, o agente principal tem autonomia, porque ele pode delegar coisas para outros agentes, ele pode executar coisas de uma forma sem a necessidade de outro cara necessariamente mandar nele ali para ele, tá?
  Confiança: -0.1537108730960202

Segmento 581:
  Início: 6187.8s
  Fim: 6211.8s
  Texto:  Então isso aí é um ponto importante, qual que é o outro aspecto aqui, né? Se o agente principal ele pode executar de forma independente autônoma, o subagente ele é executado apenas quando a gente pai decide, ele não tem controle sobre quando ele vai ser chamado, ele vai funcionar como uma função especializada do pai.
  Confiança: -0.11415814130734174

Segmento 582:
  Início: 6212.8s
  Fim: 6240.8s
  Texto:  Agora qual outra diferença de um agente para um subagente? Tem um outra diferença aí também, essa diferença tá na no escopo, pra onde eles podem transferir o controle, ou seja, o subagente ele interage com o usuário, tá? Isso é chamado de delegation. Então basicamente acontece o seguinte, o agente principal ele pode transferir o controle,
  Confiança: -0.19983096403234146

Segmento 583:
  Início: 6240.8s
  Fim: 6253.8s
  Texto:  pra qualquer agente na hierarquia dele, ele tem a visibilidade completa da estrutura organizacional e ele pode escalar problemas diretamente de cima pra baixo ou de baixo pra cima.
  Confiança: -0.10608936439860951

Segmento 584:
  Início: 6253.8s
  Fim: 6263.8s
  Texto:  Ele consegue ver tudo em volta dele, lembra que eu falei que o agente ele tem especialização e ele entende um ambiente em volta dele? Então é isso que acontece com a gente principal.
  Confiança: -0.10608936439860951

Segmento 585:
  Início: 6263.8s
  Fim: 6279.8s
  Texto:  Outra coisa interessante é que subagentes, o subagente ele é limitado pela hierarquia definida pelo pai, ou seja, ele pode interagir apenas do escopo que o pai permite, ele não pode pular nível hierarquico,
  Confiança: -0.14104750899017834

Segmento 586:
  Início: 6279.8s
  Fim: 6294.8s
  Texto:  pode dar um exemplo aqui pra vocês também, discopo, tá? Vamos imaginar aqui que eu tenho a minha empresa, companhia AI, certo? Essa minha companhia AI é a minha estrutura máxima e ela tem subagentes.
  Confiança: -0.19635975614507148

Segmento 587:
  Início: 6294.8s
  Fim: 6303.8s
  Texto:  Um agente que ela tem é chamado de customer services e agente. O customer services e agente ele tem subagentes. Quais são os subagentes dele?
  Confiança: -0.19635975614507148

Segmento 588:
  Início: 6303.8s
  Fim: 6312.8s
  Texto:  O de order look up para consultar ordem de serviço e para dar um refund, ou seja, para devolver dinheiro de ordem cancelado.
  Confiança: -0.19632462894215302

Segmento 589:
  Início: 6312.8s
  Fim: 6327.8s
  Texto:  Mas a minha empresa também tem um agente de suporte, de técnica ao suporte, que tem um subagente de diagnóstico e eu tenho um subagente de escalar o ticket quando dar algum problema.
  Confiança: -0.19632462894215302

Segmento 590:
  Início: 6328.8s
  Fim: 6338.8s
  Texto:  Legal? Então o que cada um pode fazer aqui nesse caso aqui pra gente, galera? O seguinte, o customer service e agente é o principal, tá?
  Confiança: -0.142952880859375

Segmento 591:
  Início: 6338.8s
  Fim: 6354.8s
  Texto:  Ele pode transferir as coisas para o pai dele e ele pode transferir as coisas para o técnico ao suporte e ele pode delegar para o order look up.
  Confiança: -0.142952880859375

Segmento 592:
  Início: 6355.8s
  Fim: 6370.8s
  Texto:  Ou seja, eu consigo delegar coisas para o meu irmão e eu consigo delegar coisas para os meus filhos. Ou seja, eu transfiro coisas para o meu irmão ou eu delegue coisas para os meus filhos.
  Confiança: -0.13841349344987136

Segmento 593:
  Início: 6371.8s
  Fim: 6385.8s
  Texto:  Agora, o order look up, e a gente quer esse agente aqui, ele não pode falar diretamente com o técnico ao suporte, porque ele tá fora da hierarquia do técnico ao suporte, tá?
  Confiança: -0.168469888192636

Segmento 594:
  Início: 6386.8s
  Fim: 6397.8s
  Texto:  E ele não pode mandar qualquer tipo de informação para o agente principal com a compreens AI, porque ele tá fora da hierarquia também. Entendeu?
  Confiança: -0.2836437501769135

Segmento 595:
  Início: 6397.8s
  Fim: 6407.8s
  Texto:  Então, ou seja, o escopo de cada subagente, ele é definido de acordo com a estrutura organizacional dele.
  Confiança: -0.2836437501769135

Segmento 596:
  Início: 6407.8s
  Fim: 6414.8s
  Texto:  Beleza? E tem outra coisa que é importante aqui que vocês entendam, tá? Que é o seguinte.
  Confiança: -0.13829536437988282

Segmento 597:
  Início: 6416.8s
  Fim: 6433.8s
  Texto:  O agente que suba a gente, o estado de como eles compartilham a informação são diferentes. Por exemplo, o agente principal, ele gerenci o estado dele o contexto dele.
  Confiança: -0.13829536437988282

Segmento 598:
  Início: 6434.8s
  Fim: 6441.8s
  Texto:  Ele decide quais informações que ele quer compartilhar com o subagente, e ele pode manter informações privadas só para ele.
  Confiança: -0.12518682285230986

Segmento 599:
  Início: 6442.8s
  Fim: 6454.8s
  Texto:  O subagente, ele tem que compartilhar o estado dele com o pai dele. Ele não tem estado privado. E ele trabalha com o contexto que foi fornecido pelo pai dele.
  Confiança: -0.12518682285230986

Segmento 600:
  Início: 6455.8s
  Fim: 6461.8s
  Texto:  O que isso significa na prática se a gente for olhar aqui, galera? Tá? Em relação ao estado.
  Confiança: -0.12518682285230986

Segmento 601:
  Início: 6462.8s
  Fim: 6470.8s
  Texto:  Vamos imaginar que eu tenho aqui, ó. O cliente quer cancelar um pedido. Então eu tenho um agente principal de customer service e agente.
  Confiança: -0.19711773804943972

Segmento 602:
  Início: 6471.8s
  Fim: 6485.8s
  Texto:  Eu tenho o customer ID, a história, a minha conversação com o meu cliente, eu tenho o meu nível de autorização, e eu tenho as minhas notas privadas aqui, que é ele é um cliente VIP, eu tenho que ser extra cuidadoso com ele.
  Confiança: -0.19711773804943972

Segmento 603:
  Início: 6486.8s
  Fim: 6498.8s
  Texto:  E eu tenho o meu session context, ou seja, a tarefa atual que ele quer é o cancelamento de uma ordem, e ele está frustrado. O customer muda está frustrado. Eu sei que o cliente está frustrado.
  Confiança: -0.15702814374651228

Segmento 604:
  Início: 6499.8s
  Fim: 6512.8s
  Texto:  Então, quando o cliente pede para cancelar alguma coisa, ele vai compartilhar com o agente de baixo, e vai falar o seguinte, olha, eu tenho o cliente 1, 2, 3, 4, 5.
  Confiança: -0.15702814374651228

Segmento 605:
  Início: 6513.8s
  Fim: 6534.8s
  Texto:  O contexto é que ele quer fazer um cancelamento e ele está frustrado. Perceba que eu não passei para ele, que o cliente está bravo, eu não passei para ele a conversa que eu tive com o cliente, eu não falei meu nível de autorização, eu só passei para o agente aqui de baixo, o que eu precisava passar para ele.
  Confiança: -0.11544490414996479

Segmento 606:
  Início: 6534.8s
  Fim: 6543.8s
  Texto:  Ou seja, eu escolho o que eu vou compartilhar com esse agente. Entendeu? Fez sentido, galera.
  Confiança: -0.14133844953594785

Segmento 607:
  Início: 6545.8s
  Fim: 6554.8s
  Texto:  Então, isso aí é um ponto importante para que vocês consigam se ligar. Então, como é que ficaria um fluxo completo aqui para a gente entender?
  Confiança: -0.14133844953594785

Segmento 608:
  Início: 6554.8s
  Fim: 6568.8s
  Texto:  Eu tenho um agente de customer service, que eu falo, você é o agente principal decista quando usar especialista. Aí eu tenho subagentes, order no cap, você só procure informação de pedidos.
  Confiança: -0.291088164480109

Segmento 609:
  Início: 6568.8s
  Fim: 6580.8s
  Texto:  Eu tenho o refund processor agent, você só processa a reimbouço, e eu tenho técnica o suporte agent, você só resolve problemas técnicos. Ficou claro aí para todo mundo?
  Confiança: -0.291088164480109

Segmento 610:
  Início: 6581.8s
  Fim: 6590.8s
  Texto:  Certo? Então, eu tenho um customer service que tem três subagentes ali para ele. Então, vamos imaginar o fluxo de uma conversa.
  Confiança: -0.15128499269485474

Segmento 611:
  Início: 6590.8s
  Fim: 6599.8s
  Texto:  O cliente fala, o meu pedido, um, dois, três, não chegou, e eu quero reimbouço. O agente principal recebe a mensagem, e analisa.
  Confiança: -0.15128499269485474

Segmento 612:
  Início: 6599.8s
  Fim: 6612.8s
  Texto:  Eu preciso da informação do pedido e processar o reimbouço. Aí ele decide, autonomamente, autononumamente, automano. Ele recebe de forma autônoma, chamar o subagente.
  Confiança: -0.2193670078199737

Segmento 613:
  Início: 6612.8s
  Fim: 6624.8s
  Texto:  Aí ele vai chamar quem? Order no cap e a gente. Ele vai falar o seguinte. Busque a informação do pedido, um, dois, três, e retorne os dados para o pai.
  Confiança: -0.2193670078199737

Segmento 614:
  Início: 6624.8s
  Fim: 6636.8s
  Texto:  Ele não vai responder direto para o cliente final. Então, agora, o que acontece? O agente principal recebe os dados do pedido, que o order, o cap e a gente, passou para ele.
  Confiança: -0.12812600386770148

Segmento 615:
  Início: 6636.8s
  Fim: 6649.8s
  Texto:  E ele vai verificar se o reimbouço é possível. E ele decide, chamar o refund processor agent, agora. E ele vai fazer o seguinte. Ele vai pegar o contexto do pedido,
  Confiança: -0.12812600386770148

Segmento 616:
  Início: 6650.8s
  Fim: 6658.8s
  Texto:  e vai falar, processo o reimbouço, ele vai processar, e vai retornar a confirmação do reimbouço para o agente e pai.
  Confiança: -0.12593812942504884

Segmento 617:
  Início: 6658.8s
  Fim: 6666.8s
  Texto:  O agente e pai vai montar a resposta final para o cliente. Seu pedido, um, dois, três foi encontrado, e o reimbouço de 50 reais foi processado.
  Confiança: -0.12593812942504884

Segmento 618:
  Início: 6666.8s
  Fim: 6673.8s
  Texto:  Deu para vocês conseguir entender como é que o agente consegue falar e delegar coisas para o outro?
  Confiança: -0.12593812942504884

Segmento 619:
  Início: 6674.8s
  Fim: 6689.8s
  Texto:  Fez sentido isso para vocês, galera. Agora, vocês concordam comigo que essa parada é um tipo de software completamente diferente do que a gente está acostumado a desenvolver?
  Confiança: -0.1484875568123751

Segmento 620:
  Início: 6690.8s
  Fim: 6703.8s
  Texto:  Arquitetura é outra, o jogo é outro, a tecnologia é outra, o prompt, você tem que trabalhar a forma de como você testar isso é diferente.
  Confiança: -0.18266557610553244

Segmento 621:
  Início: 6703.8s
  Fim: 6713.8s
  Texto:  Não entendeu? Ou seja, as coisas mudam completamente, não é mais o back end que a gente está acostumado, entendeu?
  Confiança: -0.18266557610553244

Segmento 622:
  Início: 6713.8s
  Fim: 6727.8s
  Texto:  E aí você tem que pensar que a gente tem que trabalhar com memória, ele tem que trabalhar com banco de dados, ele tem que trabalhar com os MCPs, com as integrações, ele tem que poder escalar, ele pode rodar com um micro serviço, ele pode fazer deploy-me com Kubernetes.
  Confiança: -0.15539321158696145

Segmento 623:
  Início: 6727.8s
  Fim: 6738.8s
  Texto:  Tudo isso, mas cara, arquitetura de agentes é diferente. Até mesmo para você arquitetar isso aqui, é um processo, cara.
  Confiança: -0.15539321158696145

Segmento 624:
  Início: 6738.8s
  Fim: 6750.8s
  Texto:  Arquitetura de agentes, cara, como que você organiza essa árvore para que fique sustentável e fácil de eu modificar isso conforme eu for adicionando outros agentes do meio da história? Entendeu?
  Confiança: -0.20299480438232423

Segmento 625:
  Início: 6750.8s
  Fim: 6764.8s
  Texto:  Eu vou trazer aqui para vocês, pessoal, uma gente, uma aplicação multa-gente que eu fiz, tá? Até gravei um vídeo no YouTube, outros eu já fiz aqui dessa forma, tá?
  Confiança: -0.20299480438232423

Segmento 626:
  Início: 6764.8s
  Fim: 6779.8s
  Texto:  Deixa eu criar uma nova seção. Eu criei, tá? Um agente principal chamado de bugfinder, tá? O que que acontece aqui nesse agente? A gente, eu tenho um objetivo aqui muito claro para esse cara.
  Confiança: -0.160135375128852

Segmento 627:
  Início: 6779.8s
  Fim: 6801.8s
  Texto:  Ele vai ficar recebendo logs no servidor, e ele vai verificar se esse log é de erro ou não é um log de erro, tá? Então vamos imaginar que eu vou passar ali para ele o seguinte log aqui para mim.
  Confiança: -0.1316627916300072

Segmento 628:
  Início: 6801.8s
  Fim: 6828.8s
  Texto:  Então ele vai receber um log, eu tenho um agente que recebe um log, ele vai processar, aí ele manda isso para um agente de análise de bugs, e esse gerenciador de análise de bugs, interpretou que esse log é de uma chamada HTTPGET, gerado pelo EngineX, e isso não é um erro logo para por aqui.
  Confiança: -0.18068624384262982

Segmento 629:
  Início: 6829.8s
  Fim: 6855.8s
  Texto:  Beleza? Aí, o que que acontece no meio das coisas, galera? Eu posso criar uma outra seção, por exemplo, e eu posso fazer algo um pouquinho diferente. Eu posso pegar aqui um log de erro, feito pelo um javeiro, e imagina que eu recebi lá no meu New Relic, recebi no meu Lesk Search, sei lá.
  Confiança: -0.17211377911451387

Segmento 630:
  Início: 6855.8s
  Fim: 6876.8s
  Texto:  E colei aqui, ó, é um erro de StackTrace, tá? Só para vocês saberem. E eu vou mandar esse cara para o meu agente. Então o que que vai acontecer? Eu tenho um agente que fica olhando para receber log, aí ele recebeu log, ele vai tomar uma decisão que ele tem que mandar isso para um outro agente que analisa para ver se isso é um bug.
  Confiança: -0.18688727439718045

Segmento 631:
  Início: 6876.8s
  Fim: 6890.8s
  Texto:  E esse agente vai analisar aqui, isso é um bug, tá? Aí, uma vez que ele decidiu que isso é um bug, ele vai criar um draft, um rascunho de uma iixo do GeekHub.
  Confiança: -0.2127532170823783

Segmento 632:
  Início: 6890.8s
  Fim: 6905.8s
  Texto:  Uma vez que ele fez esse rascunho, ele vai passar para um agente que vai revisar essa iixo que ele criou. E ele vai pegar as melhorias que ele quis nessa revisão e mandar para um agente que vai fazer o refinamento dessa iixo para ela ficar bonitista.
  Confiança: -0.2127532170823783

Segmento 633:
  Início: 6906.8s
  Fim: 6929.8s
  Texto:  E depois disso, ela passa para um agente que vai criar uma iixo no GeekHub. E depois disso, vai ser mandado para um agente que vai me notificar no Discord, que aquele meu código teve um bug e uma iixo foi criada no GeekHub ali para mim.
  Confiança: -0.11471350513287444

Segmento 634:
  Início: 6929.8s
  Fim: 6944.8s
  Texto:  É basicamente isso que acaba acontecendo ali nesse caso. Não entendeu? Como é que é a grande pegada, galera? Então tudo isso, ele acaba trazendo aqui para a gente.
  Confiança: -0.15544470151265463

Segmento 635:
  Início: 6944.8s
  Fim: 6960.8s
  Texto:  Então eu tenho request, as responces de cada evento, eu consigo colocar o que a gente respondeu e etc. Eu não sei se eu consigo perguntar, give me the iixo URL.
  Confiança: -0.23866653442382812

Segmento 636:
  Início: 6960.8s
  Fim: 6971.8s
  Texto:  Eu não sei se ele vai continuar conversa ou se ele vai chamar outro agente. Ah, ele trouxe aqui, ó. A iixo que eu gerei foi nesse endereço. Aí eu clico aqui e olha só o que ele fez, galera.
  Confiança: -0.23866653442382812

Segmento 637:
  Início: 6972.8s
  Fim: 6988.8s
  Texto:  Order stable missing em produção database. Description, the order stable missing de produção database e preventive de aplicar o fom process New Order. Foi descobrido, descoberto o estal, hoje atualmente as novas ordens estão falhando, etc.
  Confiança: -0.516619291461882

Segmento 638:
  Início: 6988.8s
  Fim: 7003.8s
  Texto:  O banco de dados é o posto gris nessa versão, o erro foiesse, do spring, do java, etc. A funcionalidade foi afetada, o impacto do usuário foi esse. Suggestões para você resolver esse problema, consequência de você não resolver esse iixo, tá?
  Confiança: -0.24860087684963061

Segmento 639:
  Início: 7003.8s
  Fim: 7007.8s
  Texto:  Ele é um bug e tem alta prioridade, que foi atribuído aqui para mim.
  Confiança: -0.24860087684963061

Segmento 640:
  Início: 7008.8s
  Fim: 7021.8s
  Texto:  Vocês entenderam, galera, o que está acontecendo no nosso mundo? Imagina que bug simples que começem a acontecer nos nossos sistemas, caiu um para um agente desse, que vai criar uma iixo.
  Confiança: -0.24626602665070566

Segmento 641:
  Início: 7021.8s
  Fim: 7034.8s
  Texto:  E daí você vai ter uma agente autônoma que vai pegar essa iixo, vai resolver o bug e vai criar uma puro request, que vai ter uma gente que vai fazer uma cor de review.
  Confiança: -0.24626602665070566

Segmento 642:
  Início: 7035.8s
  Fim: 7049.8s
  Texto:  E aí você pode decidir para um humano ou não dar um merge ou um humano revisa a cor de review. Vocês conseguem entender, galera, que loucura que dá para fazer baseado em tudo isso.
  Confiança: -0.18734287729068677

Segmento 643:
  Início: 7050.8s
  Fim: 7058.8s
  Texto:  Sacou? E tudo isso, galera, eu criei pelo ADK.
  Confiança: -0.3124111175537109

Segmento 644:
  Início: 7058.8s
  Fim: 7065.8s
  Texto:  Isso é, esse agente aqui, eu fiz utilizando o Google ADK.
  Confiança: -0.3124111175537109

Segmento 645:
  Início: 7065.8s
  Fim: 7076.8s
  Texto:  Ou seja, ele foi, e no meu Discord, aqui não vou abrir meu Discord para vocês, eu recebi uma notificação aqui, falando que um novo iixo foi adicionada ali para mim.
  Confiança: -0.3124111175537109

Segmento 646:
  Início: 7077.8s
  Fim: 7089.8s
  Texto:  Galera, vocês conseguiram perceber que naturalmente você não conseguiria criar um software dessa forma?
  Confiança: -0.16099862563304412

Segmento 647:
  Início: 7089.8s
  Fim: 7093.8s
  Texto:  Quem conseguiria criar um software que fizesse isso dessa forma?
  Confiança: -0.16099862563304412

Segmento 648:
  Início: 7093.8s
  Fim: 7096.8s
  Texto:  Sem IA, não dá para criar.
  Confiança: -0.17751797591105545

Segmento 649:
  Início: 7096.8s
  Fim: 7107.8s
  Texto:  Ponto, porque você não consegue entender um problema, escrever um texto dessa forma, entender o contexto, descobrir tecnologia, colocar funcionalidades afetadas, etc.
  Confiança: -0.17751797591105545

Segmento 650:
  Início: 7107.8s
  Fim: 7109.8s
  Texto:  Cara, simplesmente você não consegue fazer isso.
  Confiança: -0.17751797591105545

Segmento 651:
  Início: 7110.8s
  Fim: 7112.8s
  Texto:  Sacou?
  Confiança: -0.17751797591105545

Segmento 652:
  Início: 7112.8s
  Fim: 7116.8s
  Texto:  Simplesmente você não consegue fazer isso sem IA.
  Confiança: -0.17751797591105545

Segmento 653:
  Início: 7116.8s
  Fim: 7121.8s
  Texto:  Ou seu código vai ser o código que vai ter mais if else da sua vida, entendeu?
  Confiança: -0.17751797591105545

Segmento 654:
  Início: 7122.8s
  Fim: 7129.8s
  Texto:  Então, a grande sacada é basicamente nesse nível que a gente está.
  Confiança: -0.12921177161918893

Segmento 655:
  Início: 7129.8s
  Fim: 7134.8s
  Texto:  Então, por isso que eu estou dizendo que um agente de IA é muito mais que um chatbot.
  Confiança: -0.12921177161918893

Segmento 656:
  Início: 7134.8s
  Fim: 7142.8s
  Texto:  Vocês concordam comigo agora que essa interface aqui que eu estou mostrando no ADK?
  Confiança: -0.12921177161918893

Segmento 657:
  Início: 7142.8s
  Fim: 7144.8s
  Texto:  Acho que eu tenho que dar um voltar.
  Confiança: -0.12921177161918893

Segmento 658:
  Início: 7144.8s
  Fim: 7148.8s
  Texto:  Essa interface que eu estou mostrando aqui no ADK,
  Confiança: -0.12921177161918893

Segmento 659:
  Início: 7148.8s
  Fim: 7152.8s
  Texto:  ela é uma interface apenas de teste e de debug, tá?
  Confiança: -0.341298907995224

Segmento 660:
  Início: 7152.8s
  Fim: 7160.8s
  Texto:  Mas a entrada e a chamada desse agente poderia ser simplesmente o elésque search,
  Confiança: -0.341298907995224

Segmento 661:
  Início: 7160.8s
  Fim: 7164.8s
  Texto:  quando caiu um log que você manda a presscar.
  Confiança: -0.341298907995224

Segmento 662:
  Início: 7165.8s
  Fim: 7168.8s
  Texto:  Fez sentido para vocês, galera?
  Confiança: -0.341298907995224

Segmento 663:
  Início: 7169.8s
  Fim: 7170.8s
  Texto:  Tá?
  Confiança: -0.18090686325199348

Segmento 664:
  Início: 7171.8s
  Fim: 7176.8s
  Texto:  Agora, o que muita gente deve estar perguntando, é, como é que escala?
  Confiança: -0.18090686325199348

Segmento 665:
  Início: 7176.8s
  Fim: 7181.8s
  Texto:  Como é que é o custo? Galera? Isso é um buraco muito mais embaixo, tá?
  Confiança: -0.18090686325199348

Segmento 666:
  Início: 7181.8s
  Fim: 7186.8s
  Texto:  Isso aqui é um exemplo que jamais você conseguiria escalar, da forma como ele está feito, tá?
  Confiança: -0.18090686325199348

Segmento 667:
  Início: 7186.8s
  Fim: 7188.8s
  Texto:  Só para vocês saberem, tá?
  Confiança: -0.18090686325199348

Segmento 668:
  Início: 7188.8s
  Fim: 7191.8s
  Texto:  Você tem que entender de arquitetura de software, você tem que fazer coisas de custo.
  Confiança: -0.18090686325199348

Segmento 669:
  Início: 7191.8s
  Fim: 7193.8s
  Texto:  Você vai ter que trabalhar com um bet prompt, hein?
  Confiança: -0.18090686325199348

Segmento 670:
  Início: 7193.8s
  Fim: 7198.8s
  Texto:  Você vai ter que fazer com que agente façam processamentos simultâneos
  Confiança: -0.22649274048981843

Segmento 671:
  Início: 7198.8s
  Fim: 7202.8s
  Texto:  para você fazer apenas uma chamada para a LLM, para economizar token.
  Confiança: -0.22649274048981843

Segmento 672:
  Início: 7202.8s
  Fim: 7205.8s
  Texto:  Então, tem muitas estratégias para que você economize,
  Confiança: -0.22649274048981843

Segmento 673:
  Início: 7205.8s
  Fim: 7208.8s
  Texto:  para que você consiga escalar, que você consiga triar.
  Confiança: -0.22649274048981843

Segmento 674:
  Início: 7208.8s
  Fim: 7213.8s
  Texto:  Então, tem muita coisa por trás para você fazer um negócio desse rodem de produção.
  Confiança: -0.22649274048981843

Segmento 675:
  Início: 7213.8s
  Fim: 7217.8s
  Texto:  Mas o que eu estou querendo dizer aqui para vocês é que
  Confiança: -0.22649274048981843

Segmento 676:
  Início: 7218.8s
  Fim: 7223.8s
  Texto:  são infinitas as possibilidades que a gente tem agora de desenvolver software.
  Confiança: -0.20668411254882812

Segmento 677:
  Início: 7223.8s
  Fim: 7225.8s
  Texto:  Sacou?
  Confiança: -0.20668411254882812

Segmento 678:
  Início: 7227.8s
  Fim: 7229.8s
  Texto:  Fez sentido para vocês, galera.
  Confiança: -0.20668411254882812

Segmento 679:
  Início: 7232.8s
  Fim: 7233.8s
  Texto:  Manjou?
  Confiança: -0.20668411254882812

Segmento 680:
  Início: 7233.8s
  Fim: 7236.8s
  Texto:  Tudo o que vocês estão vendo aqui é o ADK.
  Confiança: -0.20668411254882812

Segmento 681:
  Início: 7236.8s
  Fim: 7241.8s
  Texto:  O ADK é bem completo, então ele tem o Framework, tá?
  Confiança: -0.20668411254882812

Segmento 682:
  Início: 7241.8s
  Fim: 7246.8s
  Texto:  Ele tem a interface web, aí, tipo, deixa eu colar,
  Confiança: -0.20668411254882812

Segmento 683:
  Início: 7246.8s
  Fim: 7250.8s
  Texto:  mas um erro aqui, sei lá, cara, deixa eu botar aqui para ele.
  Confiança: -0.20728060024887768

Segmento 684:
  Início: 7250.8s
  Fim: 7254.8s
  Texto:  Quando a hoje eu vou pegar no pé dos javeiros aqui, tá?
  Confiança: -0.20728060024887768

Segmento 685:
  Início: 7254.8s
  Fim: 7257.8s
  Texto:  Vou colocar um outro erro aqui de uma outra sessão,
  Confiança: -0.20728060024887768

Segmento 686:
  Início: 7257.8s
  Fim: 7260.8s
  Texto:  vou botar esse cara aqui para rodar.
  Confiança: -0.20728060024887768

Segmento 687:
  Início: 7262.8s
  Fim: 7265.8s
  Texto:  E eu acho que eu consigo até ver as sessões anteriores,
  Confiança: -0.20728060024887768

Segmento 688:
  Início: 7265.8s
  Fim: 7267.8s
  Texto:  mas vamos deixar esse cara rodar.
  Confiança: -0.20728060024887768

Segmento 689:
  Início: 7267.8s
  Fim: 7272.8s
  Texto:  O que é interessante também aqui, galera, é que quando a gente começa a entrar nesta aspecto,
  Confiança: -0.20728060024887768

Segmento 690:
  Início: 7272.8s
  Fim: 7275.8s
  Texto:  a gente começa a entrar em buracos muito mais embaixo.
  Confiança: -0.20728060024887768

Segmento 691:
  Início: 7275.8s
  Fim: 7277.8s
  Texto:  Como que é a observabilidade?
  Confiança: -0.11325274814258922

Segmento 692:
  Início: 7277.8s
  Fim: 7280.8s
  Texto:  Como que eu sei o que um a gente passou para o outro?
  Confiança: -0.11325274814258922

Segmento 693:
  Início: 7280.8s
  Fim: 7282.8s
  Texto:  Como que eu sei se deu um erro?
  Confiança: -0.11325274814258922

Segmento 694:
  Início: 7282.8s
  Fim: 7284.8s
  Texto:  O erro foi de quem?
  Confiança: -0.11325274814258922

Segmento 695:
  Início: 7284.8s
  Fim: 7287.8s
  Texto:  O meu sistema chamou um outro que chamou um a gente,
  Confiança: -0.11325274814258922

Segmento 696:
  Início: 7287.8s
  Fim: 7289.8s
  Texto:  que chamou um outro sistema.
  Confiança: -0.11325274814258922

Segmento 697:
  Início: 7289.8s
  Fim: 7290.8s
  Texto:  Deu pau.
  Confiança: -0.11325274814258922

Segmento 698:
  Início: 7290.8s
  Fim: 7292.8s
  Texto:  Da onde que eu consigo ver se foi o pau?
  Confiança: -0.11325274814258922

Segmento 699:
  Início: 7292.8s
  Fim: 7295.8s
  Texto:  O pau foi no a gente, foi no sistema, foi no banco de dados.
  Confiança: -0.11325274814258922

Segmento 700:
  Início: 7295.8s
  Fim: 7297.8s
  Texto:  Como é que eu consigo verificar isso?
  Confiança: -0.11325274814258922

Segmento 701:
  Início: 7297.8s
  Fim: 7300.8s
  Texto:  Como é que eu consigo verificar a qualidade
  Confiança: -0.11325274814258922

Segmento 702:
  Início: 7300.8s
  Fim: 7305.8s
  Texto:  que realmente o a gente está garantindo para mim que aquilo é um bug ou não?
  Confiança: -0.17993003181789233

Segmento 703:
  Início: 7305.8s
  Fim: 7310.8s
  Texto:  Vocês entendem que a gente tem outros problemas aí no meio de toda essa história?
  Confiança: -0.17993003181789233

Segmento 704:
  Início: 7310.8s
  Fim: 7318.8s
  Texto:  Então, o ponto importante que eu quero trazer aqui para vocês é que não é só criar o a gente.
  Confiança: -0.17993003181789233

Segmento 705:
  Início: 7318.8s
  Fim: 7321.8s
  Texto:  Se você quiser criar um a gente para a palaria da esquina,
  Confiança: -0.17993003181789233

Segmento 706:
  Início: 7321.8s
  Fim: 7325.8s
  Texto:  ok, se você quiser criar um a gente para o Itaú, para o Núíubenque,
  Confiança: -0.17993003181789233

Segmento 707:
  Início: 7325.8s
  Fim: 7327.8s
  Texto:  para o mercado livre,
  Confiança: -0.17993003181789233

Segmento 708:
  Início: 7327.8s
  Fim: 7331.8s
  Texto:  com certeza não vai ser dessa forma.
  Confiança: -0.22090840546981147

Segmento 709:
  Início: 7331.8s
  Fim: 7336.8s
  Texto:  Mas o conceito que você precisa entender é esse,
  Confiança: -0.22090840546981147

Segmento 710:
  Início: 7336.8s
  Fim: 7339.8s
  Texto:  aqui ele já trossou o link da itchio para mim.
  Confiança: -0.22090840546981147

Segmento 711:
  Início: 7339.8s
  Fim: 7342.8s
  Texto:  E aqui, galera, você consegue ver todos os eventos.
  Confiança: -0.22090840546981147

Segmento 712:
  Início: 7342.8s
  Fim: 7345.8s
  Texto:  Então você eu clico aqui, ele chamou esse cara,
  Confiança: -0.22090840546981147

Segmento 713:
  Início: 7345.8s
  Fim: 7348.8s
  Texto:  como que foi a request, você é um bug finder,
  Confiança: -0.22090840546981147

Segmento 714:
  Início: 7348.8s
  Fim: 7350.8s
  Texto:  execute isso, execute aquilo.
  Confiança: -0.22090840546981147

Segmento 715:
  Início: 7350.8s
  Fim: 7354.8s
  Texto:  Aí, ele pegou todos os passos, todos os parâmetros,
  Confiança: -0.22090840546981147

Segmento 716:
  Início: 7354.8s
  Fim: 7358.8s
  Texto:  mandou para o Itchio Drifter, depois mandou para o review,
  Confiança: -0.15653113948488698

Segmento 717:
  Início: 7358.8s
  Fim: 7361.8s
  Texto:  o review recebeu esses dados, etc.
  Confiança: -0.15653113948488698

Segmento 718:
  Início: 7361.8s
  Fim: 7366.8s
  Texto:  Então você consegue ter o conteúdo que foi gerado, entendeu?
  Confiança: -0.15653113948488698

Segmento 719:
  Início: 7366.8s
  Fim: 7369.8s
  Texto:  E aí, essa parada vai cada vez mais embaixo,
  Confiança: -0.15653113948488698

Segmento 720:
  Início: 7369.8s
  Fim: 7373.8s
  Texto:  porque você consegue pegar cada item da interação e ver como é que foi.
  Confiança: -0.15653113948488698

Segmento 721:
  Início: 7373.8s
  Fim: 7378.8s
  Texto:  Então, esse cara aqui recebeu essa request.
  Confiança: -0.15653113948488698

Segmento 722:
  Início: 7378.8s
  Fim: 7382.8s
  Texto:  E ele trouxe aqui para mim,
  Confiança: -0.15653113948488698

Segmento 723:
  Início: 7382.8s
  Fim: 7384.8s
  Texto:  essa é a resposta.
  Confiança: -0.21957315247634362

Segmento 724:
  Início: 7384.8s
  Fim: 7387.8s
  Texto:  Então, a request foi essa, a resposta foi esse,
  Confiança: -0.21957315247634362

Segmento 725:
  Início: 7387.8s
  Fim: 7390.8s
  Texto:  gastou tanto toque em fez isso, fez aquilo.
  Confiança: -0.21957315247634362

Segmento 726:
  Início: 7390.8s
  Fim: 7395.8s
  Texto:  E o mais louco de tudo é que você consegue chegar aqui,
  Confiança: -0.21957315247634362

Segmento 727:
  Início: 7395.8s
  Fim: 7398.8s
  Texto:  e ver o trace.
  Confiança: -0.21957315247634362

Segmento 728:
  Início: 7398.8s
  Fim: 7400.8s
  Texto:  E aqui eu consigo ver tudo.
  Confiança: -0.21957315247634362

Segmento 729:
  Início: 7400.8s
  Fim: 7405.8s
  Texto:  Teve invocations, executou, a gente chamou LLM,
  Confiança: -0.21957315247634362

Segmento 730:
  Início: 7405.8s
  Fim: 7408.8s
  Texto:  chamou a tu para o logger receiver agent,
  Confiança: -0.21957315247634362

Segmento 731:
  Início: 7408.8s
  Fim: 7411.8s
  Texto:  chamou esse cara, esse cara chamou LLM,
  Confiança: -0.21957315247634362

Segmento 732:
  Início: 7411.8s
  Fim: 7414.8s
  Texto:  pegou ao resultado, mandou para o bug analaiser,
  Confiança: -0.1631820582542099

Segmento 733:
  Início: 7414.8s
  Fim: 7416.8s
  Texto:  eu bug analaiser fez isso, que chamou outro,
  Confiança: -0.1631820582542099

Segmento 734:
  Início: 7416.8s
  Fim: 7419.8s
  Texto:  que chamou outro, que chamou outro, e fez isso,
  Confiança: -0.1631820582542099

Segmento 735:
  Início: 7419.8s
  Fim: 7422.8s
  Texto:  até gerar a última chamada e demorou tanto tempo
  Confiança: -0.1631820582542099

Segmento 736:
  Início: 7422.8s
  Fim: 7425.8s
  Texto:  aqui no meu processo para rodar esse processo.
  Confiança: -0.1631820582542099

Segmento 737:
  Início: 7425.8s
  Fim: 7427.8s
  Texto:  Entendeu?
  Confiança: -0.1631820582542099

Segmento 738:
  Início: 7427.8s
  Fim: 7432.8s
  Texto:  Então, cara, as coisas elas estão melhorando bastante.
  Confiança: -0.1631820582542099

Segmento 739:
  Início: 7432.8s
  Fim: 7434.8s
  Texto:  Os frameworks estão evoluindo rápido,
  Confiança: -0.1631820582542099

Segmento 740:
  Início: 7434.8s
  Fim: 7437.8s
  Texto:  as regras estão evoluindo rápido,
  Confiança: -0.1631820582542099

Segmento 741:
  Início: 7437.8s
  Fim: 7442.8s
  Texto:  e agora a forma de desenvolver software mudou.
  Confiança: -0.20585331348104213

Segmento 742:
  Início: 7442.8s
  Fim: 7447.8s
  Texto:  Galera, quando eu falei para vocês que a nova geração de software,
  Confiança: -0.20585331348104213

Segmento 743:
  Início: 7447.8s
  Fim: 7452.8s
  Texto:  que a gente vai criar, quando eu chego aqui para vocês,
  Confiança: -0.20585331348104213

Segmento 744:
  Início: 7452.8s
  Fim: 7458.8s
  Texto:  para fazer o meu jabado MBA, em arquitetura em gerido de software com IA,
  Confiança: -0.20585331348104213

Segmento 745:
  Início: 7458.8s
  Fim: 7462.8s
  Texto:  e eu falo, assuma o novo perfil de desenvolvedor na era de IA.
  Confiança: -0.20585331348104213

Segmento 746:
  Início: 7462.8s
  Fim: 7465.8s
  Texto:  Você vai ser pelo menos cinco vezes mais produtivo,
  Confiança: -0.20585331348104213

Segmento 747:
  Início: 7465.8s
  Fim: 7468.8s
  Texto:  trabalhando com os workflows que você vai aprender com a gente.
  Confiança: -0.13434309471310593

Segmento 748:
  Início: 7468.8s
  Fim: 7470.8s
  Texto:  Mas o ponto que eu estou querendo colocar aqui é
  Confiança: -0.13434309471310593

Segmento 749:
  Início: 7470.8s
  Fim: 7474.8s
  Texto:  faça a parte da criação, da nova geração,
  Confiança: -0.13434309471310593

Segmento 750:
  Início: 7474.8s
  Fim: 7478.8s
  Texto:  de aplicações orientadas as IA nas maiores empresas do mercado.
  Confiança: -0.13434309471310593

Segmento 751:
  Início: 7478.8s
  Fim: 7481.8s
  Texto:  Vocês conseguiram entender agora o que eu quero dizer
  Confiança: -0.13434309471310593

Segmento 752:
  Início: 7481.8s
  Fim: 7486.8s
  Texto:  com fazer parte da criação, da nova geração de aplicações?
  Confiança: -0.13434309471310593

Segmento 753:
  Início: 7486.8s
  Fim: 7489.8s
  Texto:  Fez sentido para vocês o que é essa nova geração?
  Confiança: -0.13434309471310593

Segmento 754:
  Início: 7489.8s
  Fim: 7491.8s
  Texto:  Porque é um paradigma novo,
  Confiança: -0.13434309471310593

Segmento 755:
  Início: 7491.8s
  Fim: 7493.8s
  Texto:  a arquitetura mudou.
  Confiança: -0.13434309471310593

Segmento 756:
  Início: 7495.8s
  Fim: 7499.8s
  Texto:  A forma de fazer devolpes SRE mudou.
  Confiança: -0.35662686030069984

Segmento 757:
  Início: 7499.8s
  Fim: 7502.8s
  Texto:  Processo desenvolver mudou.
  Confiança: -0.35662686030069984

Segmento 758:
  Início: 7502.8s
  Fim: 7503.8s
  Texto:  E o que?
  Confiança: -0.35662686030069984

